# 服务器管理功能模块部署文档

## 项目概述

本项目实现了一个完整的服务器管理功能模块，包含前端界面、后端 API、数据库设计以及第三方系统集成。该模块基于 SmartAdmin 框架开发，采用前后端分离架构。

## 功能特性

### 1. 服务器信息管理

- 服务器基本信息的增删改查
- 支持物理机和虚拟机两种类型
- 详细的硬件配置记录（CPU、内存、磁盘等）
- 网络配置管理（公网 IP、内网 IP、NAT 映射等）
- 服务器用途和描述信息管理

### 2. 第三方系统集成

- **行云管家 API 集成**：自动同步服务器数据
- **VMware vSphere API 集成**：同步虚拟机信息
- 智能数据合并策略
- 增量同步支持

### 3. 数据同步功能

- 手动同步和定时同步
- 同步日志记录和查询
- 同步状态监控
- 同步配置管理

### 4. 权限控制

- 基于角色的权限控制
- 精细化的操作权限管理

## 技术架构

### 后端技术栈

- **框架**：Spring Boot 3.5.3
- **数据库**：MySQL 8.0+
- **ORM**：MyBatis Plus 3.5.12
- **权限**：Sa-Token
- **API 文档**：Knife4j
- **工具类**：Hutool

### 前端技术栈

- **框架**：Vue 3
- **UI 组件**：Ant Design Vue
- **构建工具**：Vite
- **HTTP 客户端**：Axios

## 部署准备

### 1. 环境要求

- JDK 17+
- MySQL 8.0+
- Node.js 16+
- npm/yarn/pnpm

### 2. 数据库配置

执行 SQL 脚本创建相关表结构：

```bash
# 执行数据库脚本
mysql -u root -p < sql/server_management.sql
```

### 3. 第三方 API 配置

#### 行云管家 API 配置

1. 获取行云管家 API 访问凭证
2. 在数据库中更新同步配置：

```sql
UPDATE t_server_sync_config
SET api_key = 'your_api_key', api_secret = 'your_api_secret'
WHERE sync_source = 'xingyun';
```

#### VMware vSphere API 配置

1. 确保 vCenter Server 可访问
2. 配置用户名和密码：

```sql
UPDATE t_server_sync_config
SET username = 'your_username', password = 'your_password'
WHERE sync_source = 'vmware';
```

## 后端部署

### 1. 编译项目

```bash
cd soms-api
mvn clean package -DskipTests
```

### 2. 配置文件

修改 `application.yaml` 配置文件：

```yaml
# 数据库配置
spring:
  datasource:
    url: ****************************************************************************************************************************************
    username: your_username
    password: your_password

# 第三方API配置（可选，也可通过管理界面配置）
server:
  sync:
    xingyun:
      api-url: https://base.xmut.edu.cn/api/openapi
      api-key: your_api_key
      api-secret: your_api_secret
    vmware:
      api-url: https://vcenter.example.com/api
      username: your_username
      password: your_password
```

### 3. 启动服务

```bash
java -jar soms-admin/target/soms-admin-3.0.0.jar
```

## 前端部署

### 1. 安装依赖

```bash
cd soms-web-antdv
pnpm install
```

### 2. 配置 API 地址

修改 `src/config/app-config.js`：

```javascript
export const APP_CONFIG = {
  // API基础地址
  API_BASE_URL: "http://localhost:8080",
  // ...其他配置
};
```

### 3. 构建项目

```bash
# 开发环境
pnpm run dev

# 生产环境
pnpm run build
```

### 4. 部署到 Nginx

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 权限配置

### 1. 添加菜单权限

在系统管理 -> 菜单管理中添加以下菜单：

```
业务管理
├── 服务器管理
    ├── 服务器列表 (server:query)
    ├── 新增服务器 (server:add)
    ├── 编辑服务器 (server:update)
    ├── 删除服务器 (server:delete)
    └── 数据同步 (server:sync)
```

### 2. 角色权限分配

根据实际需要为不同角色分配相应权限：

- **系统管理员**：所有权限
- **运维人员**：查询、新增、编辑、同步权限
- **普通用户**：仅查询权限

## 定时任务配置

### 1. 启用定时同步

系统默认配置了以下定时任务：

- **每小时检查**：根据配置的同步间隔自动执行同步
- **行云管家同步**：每天凌晨 2 点执行
- **VMware 同步**：每天凌晨 3 点执行

### 2. 自定义同步间隔

可通过管理界面或直接修改数据库配置同步间隔：

```sql
-- 设置行云管家每6小时同步一次
UPDATE t_server_sync_config
SET sync_interval = 21600
WHERE sync_source = 'xingyun';
```

## 监控和维护

### 1. 日志监控

- **应用日志**：查看 `logs/` 目录下的日志文件
- **同步日志**：通过管理界面查看同步日志
- **错误处理**：关注 ERROR 级别的日志

### 2. 性能优化

- **数据库索引**：确保关键字段已建立索引
- **定时任务**：根据数据量调整同步频率
- **缓存策略**：可考虑对频繁查询的数据进行缓存

### 3. 备份策略

- **数据库备份**：定期备份服务器数据
- **配置备份**：备份同步配置信息
- **日志归档**：定期清理和归档同步日志

## 故障排除

### 1. 常见问题

#### 数据同步失败

- 检查第三方 API 配置是否正确
- 确认网络连接是否正常
- 查看同步日志中的错误信息

#### 前端页面无法访问

- 检查后端服务是否启动
- 确认 API 地址配置是否正确
- 查看浏览器控制台错误信息

#### 权限问题

- 确认用户是否分配了相应权限
- 检查菜单和按钮权限配置

### 2. 调试模式

开发环境下可开启调试模式：

```yaml
logging:
  level:
    cn.edu.xmut.soms.admin.module.business.server: DEBUG
```

## 扩展开发

### 1. 添加新的第三方系统

1. 实现 `IThirdPartyApiService` 接口
2. 在 `ServerSyncService` 中添加相应的调用逻辑
3. 更新数据源枚举和配置

### 2. 自定义字段

1. 修改数据库表结构
2. 更新实体类和 VO 类
3. 修改前端表单和列表页面

### 3. 增强同步策略

可根据业务需求定制更复杂的数据合并和冲突处理策略。

## 联系信息

如有问题或建议，请联系：

- **开发者**：Kerwin
- **邮箱**：<EMAIL>
- **文档版本**：v1.0
- **更新日期**：2025-07-09
