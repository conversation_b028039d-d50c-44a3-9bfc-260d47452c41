# 服务器管理功能使用说明

## 功能概述

服务器管理模块提供了完整的服务器信息管理功能，支持手动录入和第三方系统自动同步两种方式。主要功能包括：

- 服务器基本信息管理
- 第三方系统数据同步
- 同步日志查询
- 同步配置管理

## 页面访问

登录系统后，导航至：**业务管理 > 服务器管理**

## 功能详细说明

### 1. 服务器列表

#### 查询功能

- **服务器名称**：支持模糊查询
- **服务器类型**：物理机、虚拟机
- **IP 地址**：支持公网 IP 和内网 IP 查询
- **主机状态**：运行状态筛选
- **数据来源**：行云管家、VMware、手动录入
- **关联系统**：是否关联信息系统

#### 列表显示

列表展示以下关键信息：

- 服务器名称
- 服务器类型（标签显示）
- 操作系统
- 公网 IP/内网 IP
- 主机状态（彩色标签）
- CPU 核心数
- 内存大小
- 数据来源（标签显示）
- 是否关联系统
- 最后同步时间

#### 操作功能

- **编辑**：修改服务器信息
- **详情**：查看完整的服务器信息
- **删除**：删除服务器记录

### 2. 服务器管理

#### 新建服务器

点击「新建」按钮，填写以下信息：

**基本信息**

- 服务器名称（必填）
- 服务器类型（必选）：物理机/虚拟机
- 操作系统
- 公网 IP 地址
- 内网 IP 地址
- 主机状态

**硬件配置**

- CPU 核心数
- 内存大小（GB）
- 系统盘大小（GB）
- 数据盘大小（GB）

**网络配置**

- 手动录入 IP
- NAT 映射 IP
- 是否关联信息系统
- 是否可访问互联网

**描述信息**

- 主机描述信息
- 服务器用途描述

#### 编辑服务器

- 点击列表中的「编辑」按钮
- 修改需要更新的字段
- 保存更改

#### 删除服务器

- **单个删除**：点击列表中的「删除」按钮
- **批量删除**：选中多条记录后点击「批量删除」按钮

### 3. 数据同步

#### 手动同步

1. 点击「数据同步」按钮
2. 选择同步来源：
   - **行云管家**：从行云管家系统同步服务器数据
   - **VMware vSphere**：从 VMware vCenter 同步虚拟机数据
3. 选择同步类型：手动同步/定时同步
4. 点击确定开始同步

#### 同步策略

- **数据合并**：以第三方系统数据为主，保留手动录入的补充信息
- **冲突处理**：API 获取的数据优先级高于系统原有数据
- **新增处理**：第三方系统独有的服务器会自动新增
- **增量同步**：支持增量更新，提高同步效率

#### 同步日志

在同步弹窗中可以查看最近的同步记录：

- 同步来源
- 同步状态（成功/失败/进行中）
- 记录统计（总数/成功/失败）
- 同步时间
- 耗时统计

### 4. 服务器详情

点击「详情」按钮可查看服务器的完整信息：

#### 基本信息

- 所有字段的详细信息
- 彩色标签显示状态信息
- 时间信息（创建、更新、同步时间）

#### 描述信息

- 主机描述
- 服务器用途说明

#### IP 地址列表

- 如果存储了 JSON 格式的 IP 列表，会以标签形式展示

### 5. 权限说明

不同权限对应的功能：

| 权限          | 功能                       |
| ------------- | -------------------------- |
| server:query  | 查询服务器信息、查看详情   |
| server:add    | 新建服务器                 |
| server:update | 编辑服务器信息             |
| server:delete | 删除服务器（单个/批量）    |
| server:sync   | 执行数据同步、查看同步日志 |

## 数据字段说明

### 服务器类型

- **0**：物理机
- **1**：虚拟机

### 数据来源

- **xingyun**：行云管家
- **vmware**：VMware vSphere
- **manual**：手动录入

### 主机状态

常见状态包括：

- **running**：运行中（绿色）
- **stopped**：已停止（红色）
- **pending**：等待中（橙色）
- **unknown**：未知状态（灰色）

### 布尔字段

- **关联信息系统**：0-否，1-是
- **访问互联网**：0-否，1-是

## 使用技巧

### 1. 高效查询

- 使用组合条件进行精确查询
- 利用数据来源筛选快速定位
- 重置按钮可快速清空查询条件

### 2. 批量操作

- 使用表格多选功能进行批量删除
- 注意批量操作不可撤销

### 3. 数据同步

- 建议在非业务高峰期进行大批量同步
- 定期查看同步日志确保数据一致性
- 同步失败时查看错误信息进行问题排查

### 4. 数据维护

- 定期清理无用的服务器记录
- 及时更新服务器状态信息
- 保持描述信息的准确性

## 注意事项

### 1. 数据安全

- 第三方 API 凭证信息会进行脱敏显示
- 删除操作请谨慎进行，避免误删重要数据

### 2. 同步机制

- 同步过程中请勿重复操作
- 同步失败不会影响现有数据
- 系统会自动记录所有同步操作

### 3. 性能考虑

- 大量数据查询时请使用分页功能
- 避免频繁的全量同步操作

### 4. 错误处理

- 操作失败时会显示具体错误信息
- 表单验证错误请按提示修改
- 网络异常时请稍后重试

## 常见问题

### Q1：为什么同步失败？

**A1：** 可能的原因：

- 第三方 API 配置错误
- 网络连接问题
- API 访问权限不足
- 第三方系统服务异常

**解决方法：**

- 检查同步配置是否正确
- 确认网络连接正常
- 查看同步日志中的详细错误信息

### Q2：数据同步后信息不一致怎么办？

**A2：**

- 检查第三方系统中的原始数据
- 确认同步映射关系是否正确
- 可以手动修正不一致的信息

### Q3：如何批量导入服务器信息？

**A3：**

- 优先使用第三方系统同步功能
- 对于特殊数据可以逐条手动录入
- 也可以联系管理员进行数据导入

### Q4：权限不足无法操作怎么办？

**A4：**

- 联系系统管理员分配相应权限
- 确认当前角色是否包含所需权限

## 联系支持

如遇到问题或有改进建议，请联系：

- **技术支持**：<EMAIL>
- **文档版本**：v1.0
- **更新日期**：2025-07-09
