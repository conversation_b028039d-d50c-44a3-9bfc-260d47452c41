-- --------------------------------------------------------
-- 服务器管理模块数据库表结构
-- 创建时间: 2025-07-09
-- 描述: 服务器管理功能相关的数据库表结构设计
-- Author: Kerwin
-- --------------------------------------------------------

USE `soms`;

-- ----------------------------
-- Table structure for t_server
-- ----------------------------
DROP TABLE IF EXISTS `t_server`;
CREATE TABLE `t_server` (
  `server_id` bigint NOT NULL AUTO_INCREMENT COMMENT '服务器唯一标识',
  `server_type` tinyint NOT NULL DEFAULT '0' COMMENT '服务器类型：0-物理机，1-虚拟机',
  `operating_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统信息',
  `public_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公网IP地址',
  `private_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内网IP地址',
  `host_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主机运行状态',
  `cpu_cores` int DEFAULT NULL COMMENT 'CPU核心数量',
  `memory_size` decimal(10,2) DEFAULT NULL COMMENT '内存大小，单位GB',
  `host_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '主机描述信息',
  `system_disk_size` decimal(10,2) DEFAULT NULL COMMENT '系统盘大小，单位GB',
  `data_disk_size` decimal(10,2) DEFAULT NULL COMMENT '数据盘大小，单位GB',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识：0-未删除，1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  `is_linked_system` tinyint NOT NULL DEFAULT '0' COMMENT '是否关联信息系统：0-否，1-是',
  `internet_access` tinyint NOT NULL DEFAULT '0' COMMENT '是否可访问互联网：0-否，1-是',
  `usage_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用状态',
  `purpose` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '服务器用途描述',
  `ip_list` json DEFAULT NULL COMMENT 'IP地址列表，JSON格式存储',
  `manual_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手动录入的IP地址',
  `nat_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NAT映射IP地址',
  `hostname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主机名',
  `server_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务器名称',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务器位置',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `responsible_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '负责人',
  `contact_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系方式',
  `purchase_date` date DEFAULT NULL COMMENT '采购日期',
  `warranty_date` date DEFAULT NULL COMMENT '保修到期日期',
  `vendor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '厂商',
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '型号',
  `serial_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '序列号',
  `asset_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资产编号',
  `sync_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL-手动录入，XINGYUN-行云管家，VMWARE-VMware',
  `external_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部系统ID',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `sync_status` tinyint DEFAULT '0' COMMENT '同步状态：0-正常，1-同步失败',
  `sync_error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '同步错误信息',
  PRIMARY KEY (`server_id`),
  KEY `idx_server_type` (`server_type`),
  KEY `idx_host_status` (`host_status`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_sync_source` (`sync_source`),
  KEY `idx_external_id` (`external_id`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器信息表';

-- ----------------------------
-- Table structure for t_server_sync_log
-- ----------------------------
DROP TABLE IF EXISTS `t_server_sync_log`;
CREATE TABLE `t_server_sync_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `sync_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步类型：MANUAL-手动同步，AUTO-自动同步',
  `sync_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步来源：XINGYUN-行云管家，VMWARE-VMware',
  `sync_status` tinyint NOT NULL COMMENT '同步状态：0-进行中，1-成功，2-失败',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `total_count` int DEFAULT '0' COMMENT '总数量',
  `success_count` int DEFAULT '0' COMMENT '成功数量',
  `fail_count` int DEFAULT '0' COMMENT '失败数量',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `sync_detail` json DEFAULT NULL COMMENT '同步详情，JSON格式',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_sync_source` (`sync_source`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器同步日志表';

-- ----------------------------
-- Table structure for t_server_monitor
-- ----------------------------
DROP TABLE IF EXISTS `t_server_monitor`;
CREATE TABLE `t_server_monitor` (
  `monitor_id` bigint NOT NULL AUTO_INCREMENT COMMENT '监控记录ID',
  `server_id` bigint NOT NULL COMMENT '服务器ID',
  `cpu_usage` decimal(5,2) DEFAULT NULL COMMENT 'CPU使用率(%)',
  `memory_usage` decimal(5,2) DEFAULT NULL COMMENT '内存使用率(%)',
  `disk_usage` decimal(5,2) DEFAULT NULL COMMENT '磁盘使用率(%)',
  `network_in` bigint DEFAULT NULL COMMENT '网络入流量(bytes)',
  `network_out` bigint DEFAULT NULL COMMENT '网络出流量(bytes)',
  `load_average` decimal(10,2) DEFAULT NULL COMMENT '系统负载',
  `uptime` bigint DEFAULT NULL COMMENT '运行时间(秒)',
  `monitor_time` datetime NOT NULL COMMENT '监控时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`monitor_id`),
  KEY `idx_server_id` (`server_id`),
  KEY `idx_monitor_time` (`monitor_time`),
  CONSTRAINT `fk_server_monitor_server_id` FOREIGN KEY (`server_id`) REFERENCES `t_server` (`server_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器监控数据表';

-- ----------------------------
-- 初始化数据字典
-- ----------------------------
INSERT INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`) VALUES
('服务器类型', 'server_type', '服务器类型枚举', 0),
('服务器状态', 'server_status', '服务器运行状态枚举', 0),
('使用状态', 'usage_status', '服务器使用状态枚举', 0),
('同步来源', 'sync_source', '数据同步来源枚举', 0);

-- ----------------------------
-- 初始化字典值
-- ----------------------------
INSERT INTO `t_dict_value` (`dict_id`, `value_code`, `value_name`, `sort`, `remark`, `disabled_flag`) VALUES
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_type'), '0', '物理机', 1, '物理服务器', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_type'), '1', '虚拟机', 2, '虚拟服务器', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_status'), 'RUNNING', '运行中', 1, '服务器正常运行', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_status'), 'STOPPED', '已停止', 2, '服务器已停止', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_status'), 'MAINTENANCE', '维护中', 3, '服务器维护中', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'server_status'), 'ERROR', '异常', 4, '服务器异常', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'usage_status'), 'IN_USE', '使用中', 1, '正在使用', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'usage_status'), 'IDLE', '空闲', 2, '空闲状态', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'usage_status'), 'RESERVED', '预留', 3, '预留状态', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'usage_status'), 'DECOMMISSIONED', '已下线', 4, '已下线', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'sync_source'), 'MANUAL', '手动录入', 1, '手动录入数据', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'sync_source'), 'XINGYUN', '行云管家', 2, '行云管家同步', 0),
((SELECT dict_id FROM t_dict WHERE dict_code = 'sync_source'), 'VMWARE', 'VMware', 3, 'VMware同步', 0);
