package cn.edu.xmut.soms.base.module.business.cloudbility.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建用户表单
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@Schema(description = "创建用户表单")
public class UserCreateForm {

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    @NotBlank(message = "用户昵称不能为空")
    private String name;

    /**
     * 性别 1-男 2-女
     */
    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    private Integer gender;

    /**
     * 登录账户
     */
    @Schema(description = "登录账户")
    private String account;

    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String password;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    /**
     * 认证方式
     */
    @Schema(description = "认证方式")
    private Integer authType;

    /**
     * 认证服务器名称
     */
    @Schema(description = "认证服务器名称")
    private String authServerName;

    /**
     * 首次登录强制修改密码
     */
    @Schema(description = "首次登录强制修改密码")
    private Boolean forceUpdatePassword;

    /**
     * 加入团队ID
     */
    @Schema(description = "加入团队ID")
    private Long joinTeam;

    /**
     * 角色权限
     */
    @Schema(description = "角色权限")
    private String roles;

    /**
     * 是否发送通知邮件
     */
    @Schema(description = "是否发送通知邮件")
    private Boolean sendEmail;
}
