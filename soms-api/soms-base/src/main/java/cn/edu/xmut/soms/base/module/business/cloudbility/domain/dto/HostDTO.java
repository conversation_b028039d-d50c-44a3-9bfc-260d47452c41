package cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 主机信息DTO
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HostDTO extends CloudbilityBaseResponse {

    /**
     * 主机ID
     */
    private Long hostId;

    /**
     * 云账户ID
     */
    private Long cloudId;

    /**
     * 云账户名称
     */
    private String cloudName;

    /**
     * 主机名称
     */
    private String instanceName;

    /**
     * 主机实例ID
     */
    private String instanceId;

    /**
     * 内网IP
     */
    private String innerIp;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * 主机状态
     */
    private String status;

    /**
     * 主机平台
     */
    private String platform;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * CPU核数
     */
    private Integer processorCores;

    /**
     * 内存（MB）
     */
    private Integer memory;

    /**
     * 主机描述
     */
    private String description;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 可用区ID
     */
    private String zoneId;

    /**
     * Agent状态
     */
    private String agentStatus;

    /**
     * 是否安装了Agent
     */
    private Boolean installedAgent;

    /**
     * 导入时间
     */
    private LocalDateTime importTime;

    /**
     * 到期时间
     */
    private LocalDateTime expiredTime;

    /**
     * 所属团队ID
     */
    private Long teamId;

    /**
     * 云账户来源
     */
    private String scope;
}
