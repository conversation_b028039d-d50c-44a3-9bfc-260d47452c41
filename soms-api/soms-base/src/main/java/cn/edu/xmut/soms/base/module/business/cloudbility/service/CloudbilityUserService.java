package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.config.CloudbilityConfig;
import cn.edu.xmut.soms.base.module.business.cloudbility.constant.CloudbilityConst;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.UserDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.UserCreateForm;
import cn.edu.xmut.soms.base.module.business.cloudbility.manager.CloudbilityHttpManager;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家用户管理服务
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Service
public class CloudbilityUserService {

    @Resource
    private CloudbilityHttpManager httpManager;

    @Resource
    private CloudbilityConfig cloudbilityConfig;

    /**
     * 创建用户
     */
    public ResponseDTO<UserDTO> createUser(UserCreateForm form) {
        try {
            Map<String, Object> params = buildUserCreateParams(form);
            
            Map<String, Object> response = httpManager.post(CloudbilityConst.API_USER, params, 
                new TypeReference<Map<String, Object>>() {});
            
            if (response.containsKey("userId")) {
                // 创建成功，获取用户详情
                Long userId = Long.valueOf(response.get("userId").toString());
                return getUserById(userId);
            } else {
                return ResponseDTO.error("创建用户失败");
            }
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return ResponseDTO.error("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取用户信息
     */
    public ResponseDTO<UserDTO> getUserById(Long userId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String path = CloudbilityConst.API_USER + "/" + userId;
            
            UserDTO userDTO = httpManager.get(path, params, new TypeReference<UserDTO>() {});
            return ResponseDTO.ok(userDTO);
        } catch (Exception e) {
            log.error("获取用户信息失败, userId: {}", userId, e);
            return ResponseDTO.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据账号获取用户信息
     */
    public ResponseDTO<UserDTO> getUserByAccount(String account) {
        try {
            Map<String, Object> params = new HashMap<>();
            String path = CloudbilityConst.API_USER + "/byAccount/" + account;
            
            UserDTO userDTO = httpManager.get(path, params, new TypeReference<UserDTO>() {});
            return ResponseDTO.ok(userDTO);
        } catch (Exception e) {
            log.error("根据账号获取用户信息失败, account: {}", account, e);
            return ResponseDTO.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取用户列表
     */
    public ResponseDTO<PageResult<UserDTO>> queryUsers(Integer page, Integer size) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page != null ? page : 1);
            params.put("size", size != null ? size : 20);
            
            Map<String, Object> response = httpManager.get(CloudbilityConst.API_USER + "s", params, 
                new TypeReference<Map<String, Object>>() {});
            
            // 解析分页结果
            PageResult<UserDTO> pageResult = parsePageResult(response, UserDTO.class);
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return ResponseDTO.error("查询用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    public ResponseDTO<String> updateUser(Long userId, UserCreateForm form) {
        try {
            Map<String, Object> params = buildUserUpdateParams(form);
            String path = CloudbilityConst.API_USER + "/" + userId;
            
            httpManager.put(path, params, new TypeReference<Map<String, Object>>() {});
            return ResponseDTO.ok("更新用户信息成功");
        } catch (Exception e) {
            log.error("更新用户信息失败, userId: {}", userId, e);
            return ResponseDTO.error("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    public ResponseDTO<String> deleteUser(Long userId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String path = CloudbilityConst.API_USER + "/" + userId;
            
            httpManager.delete(path, params, new TypeReference<Map<String, Object>>() {});
            return ResponseDTO.ok("删除用户成功");
        } catch (Exception e) {
            log.error("删除用户失败, userId: {}", userId, e);
            return ResponseDTO.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 锁定用户
     */
    public ResponseDTO<String> blockUser(Long userId, Integer blockingSeconds, String description) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("blockingSeconds", blockingSeconds != null ? blockingSeconds : 0);
            if (description != null) {
                params.put("description", description);
            }
            
            String path = CloudbilityConst.API_USER + "/" + userId + "/blocked";
            httpManager.put(path, params, new TypeReference<Map<String, Object>>() {});
            return ResponseDTO.ok("锁定用户成功");
        } catch (Exception e) {
            log.error("锁定用户失败, userId: {}", userId, e);
            return ResponseDTO.error("锁定用户失败: " + e.getMessage());
        }
    }

    /**
     * 解锁用户
     */
    public ResponseDTO<String> unblockUser(Long userId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String path = CloudbilityConst.API_USER + "/" + userId + "/unblocked";
            
            httpManager.put(path, params, new TypeReference<Map<String, Object>>() {});
            return ResponseDTO.ok("解锁用户成功");
        } catch (Exception e) {
            log.error("解锁用户失败, userId: {}", userId, e);
            return ResponseDTO.error("解锁用户失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    public ResponseDTO<String> resetPassword(Long userId, String newPassword, Boolean sendEmail) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("password", newPassword);
            if (sendEmail != null) {
                params.put("sendEmail", sendEmail);
            }
            
            String path = CloudbilityConst.API_USER + "/" + userId + "/password";
            httpManager.put(path, params, new TypeReference<Map<String, Object>>() {});
            return ResponseDTO.ok("重置密码成功");
        } catch (Exception e) {
            log.error("重置用户密码失败, userId: {}", userId, e);
            return ResponseDTO.error("重置密码失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的团队列表
     */
    public ResponseDTO<List<Map<String, Object>>> getUserTeams(Long userId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String path = CloudbilityConst.API_USER + "/" + userId + "/joinedTeams";
            
            List<Map<String, Object>> teams = httpManager.get(path, params, 
                new TypeReference<List<Map<String, Object>>>() {});
            return ResponseDTO.ok(teams);
        } catch (Exception e) {
            log.error("获取用户团队列表失败, userId: {}", userId, e);
            return ResponseDTO.error("获取用户团队列表失败: " + e.getMessage());
        }
    }

    // -------------- 私有方法 ------------------------

    /**
     * 构建创建用户参数
     */
    private Map<String, Object> buildUserCreateParams(UserCreateForm form) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", form.getName());
        params.put("gender", form.getGender());
        params.put("authType", form.getAuthType() != null ? form.getAuthType() : 1);
        
        if (form.getAccount() != null) {
            params.put("account", form.getAccount());
        }
        if (form.getPassword() != null) {
            params.put("password", form.getPassword());
        }
        if (form.getEmail() != null) {
            params.put("email", form.getEmail());
        }
        if (form.getPhone() != null) {
            params.put("phone", form.getPhone());
        }
        if (form.getAddress() != null) {
            params.put("address", form.getAddress());
        }
        if (form.getAuthServerName() != null) {
            params.put("authServerName", form.getAuthServerName());
        }
        if (form.getForceUpdatePassword() != null) {
            params.put("forceUpdatePassword", form.getForceUpdatePassword());
        }
        if (form.getJoinTeam() != null) {
            params.put("joinTeam", form.getJoinTeam());
        }
        if (form.getRoles() != null) {
            params.put("roles", form.getRoles());
        }
        if (form.getSendEmail() != null) {
            params.put("sendEmail", form.getSendEmail());
        }
        
        return params;
    }

    /**
     * 构建更新用户参数
     */
    private Map<String, Object> buildUserUpdateParams(UserCreateForm form) {
        Map<String, Object> params = new HashMap<>();
        
        if (form.getName() != null) {
            params.put("name", form.getName());
        }
        if (form.getEmail() != null) {
            params.put("email", form.getEmail());
        }
        if (form.getPhone() != null) {
            params.put("phone", form.getPhone());
        }
        if (form.getAddress() != null) {
            params.put("address", form.getAddress());
        }
        if (form.getGender() != null) {
            params.put("gender", form.getGender());
        }
        
        return params;
    }

    /**
     * 解析分页结果
     */
    @SuppressWarnings("unchecked")
    private <T> PageResult<T> parsePageResult(Map<String, Object> response, Class<T> clazz) {
        PageResult<T> pageResult = new PageResult<>();
        
        if (response.containsKey("total")) {
            pageResult.setTotal(Long.valueOf(response.get("total").toString()));
        }
        if (response.containsKey("records")) {
            List<Map<String, Object>> records = (List<Map<String, Object>>) response.get("records");
            // 这里需要根据实际情况转换为具体的DTO对象
            // 为了简化，这里暂时返回原始数据
            pageResult.setList((List<T>) records);
        }
        
        return pageResult;
    }
}
