package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.config.CloudbilityConfig;
import cn.edu.xmut.soms.base.module.business.cloudbility.constant.CloudbilityConst;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.TeamDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.TeamCreateForm;
import cn.edu.xmut.soms.base.module.business.cloudbility.manager.CloudbilityHttpManager;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家团队管理服务
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Service
public class CloudbilityTeamService {

    @Resource
    private CloudbilityHttpManager httpManager;

    @Resource
    private CloudbilityConfig cloudbilityConfig;

    /**
     * 创建团队
     */
    public ResponseDTO<TeamDTO> createTeam(TeamCreateForm form) {
        try {
            Map<String, Object> params = buildTeamCreateParams(form);
            
            Map<String, Object> response = httpManager.post(CloudbilityConst.API_TEAM, params, 
                new TypeReference<Map<String, Object>>() {});
            
            if (response.containsKey("teamId")) {
                // 创建成功，获取团队详情
                Long teamId = Long.valueOf(response.get("teamId").toString());
                return getTeamById(teamId);
            } else {
                return ResponseDTO.error("创建团队失败");
            }
        } catch (Exception e) {
            log.error("创建团队失败", e);
            return ResponseDTO.error("创建团队失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取团队详情
     */
    public ResponseDTO<TeamDTO> getTeamById(Long teamId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("teamId", teamId);
            
            TeamDTO teamDTO = httpManager.get(CloudbilityConst.API_TEAM, params, 
                new TypeReference<TeamDTO>() {});
            
            return ResponseDTO.ok(teamDTO);
        } catch (Exception e) {
            log.error("获取团队详情失败", e);
            return ResponseDTO.error("获取团队详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队列表
     */
    public ResponseDTO<PageResult<TeamDTO>> getTeamList(Integer page, Integer size, String keyword) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (page != null) {
                params.put("page", page);
            }
            if (size != null) {
                params.put("size", size);
            }
            if (StringUtils.hasText(keyword)) {
                params.put("keyword", keyword);
            }
            
            Map<String, Object> response = httpManager.get(CloudbilityConst.API_TEAM, params,
                new TypeReference<Map<String, Object>>() {});
            
            return buildPageResult(response, new TypeReference<TeamDTO>() {});
        } catch (Exception e) {
            log.error("获取团队列表失败", e);
            return ResponseDTO.error("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新团队信息
     */
    public ResponseDTO<TeamDTO> updateTeam(Long teamId, TeamCreateForm form) {
        try {
            Map<String, Object> params = buildTeamCreateParams(form);
            params.put("teamId", teamId);
            
            httpManager.put(CloudbilityConst.API_TEAM, params, 
                new TypeReference<Map<String, Object>>() {});
            
            // 返回更新后的团队信息
            return getTeamById(teamId);
        } catch (Exception e) {
            log.error("更新团队信息失败", e);
            return ResponseDTO.error("更新团队信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除团队
     */
    public ResponseDTO<Boolean> deleteTeam(Long teamId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("teamId", teamId);
            
            httpManager.delete(CloudbilityConst.API_TEAM, params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("删除团队失败", e);
            return ResponseDTO.error("删除团队失败: " + e.getMessage());
        }
    }

    /**
     * 添加团队成员
     */
    public ResponseDTO<Boolean> addTeamMembers(Long teamId, List<Long> memberIds, List<String> memberAccounts) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("teamId", teamId);
            
            if (memberIds != null && !memberIds.isEmpty()) {
                params.put("members", String.join(",", memberIds.stream().map(String::valueOf).toArray(String[]::new)));
            }
            if (memberAccounts != null && !memberAccounts.isEmpty()) {
                params.put("memberAccounts", String.join(",", memberAccounts));
            }
            
            httpManager.post(CloudbilityConst.API_TEAM + "/member", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("添加团队成员失败", e);
            return ResponseDTO.error("添加团队成员失败: " + e.getMessage());
        }
    }

    /**
     * 移除团队成员
     */
    public ResponseDTO<Boolean> removeTeamMembers(Long teamId, List<Long> memberIds, List<String> memberAccounts) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("teamId", teamId);
            
            if (memberIds != null && !memberIds.isEmpty()) {
                params.put("members", String.join(",", memberIds.stream().map(String::valueOf).toArray(String[]::new)));
            }
            if (memberAccounts != null && !memberAccounts.isEmpty()) {
                params.put("memberAccounts", String.join(",", memberAccounts));
            }
            
            httpManager.delete(CloudbilityConst.API_TEAM + "/member", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("移除团队成员失败", e);
            return ResponseDTO.error("移除团队成员失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队成员列表
     */
    public ResponseDTO<PageResult<Map<String, Object>>> getTeamMembers(Long teamId, Integer page, Integer size) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("teamId", teamId);
            if (page != null) {
                params.put("page", page);
            }
            if (size != null) {
                params.put("size", size);
            }
            
            Map<String, Object> response = httpManager.get(CloudbilityConst.API_TEAM + "/member", params,
                new TypeReference<Map<String, Object>>() {});
            
            return buildPageResult(response, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.error("获取团队成员列表失败", e);
            return ResponseDTO.error("获取团队成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 构建团队创建参数
     */
    private Map<String, Object> buildTeamCreateParams(TeamCreateForm form) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(form.getTeamName())) {
            params.put("teamName", form.getTeamName());
        }
        if (StringUtils.hasText(form.getTeamCode())) {
            params.put("teamCode", form.getTeamCode());
        }
        if (form.getCreatorId() != null) {
            params.put("creatorId", form.getCreatorId());
        }
        if (StringUtils.hasText(form.getCreatorAccount())) {
            params.put("creatorAccount", form.getCreatorAccount());
        }
        if (form.getMemberQuota() != null) {
            params.put("memberQuota", form.getMemberQuota());
        }
        if (StringUtils.hasText(form.getMembers())) {
            params.put("members", form.getMembers());
        }
        if (StringUtils.hasText(form.getMemberAccounts())) {
            params.put("memberAccounts", form.getMemberAccounts());
        }
        
        return params;
    }

    /**
     * 构建分页结果
     */
    private <T> ResponseDTO<PageResult<T>> buildPageResult(Map<String, Object> response, TypeReference<T> typeRef) {
        try {
            PageResult<T> pageResult = new PageResult<>();
            
            if (response.containsKey("content")) {
                @SuppressWarnings("unchecked")
                List<Object> content = (List<Object>) response.get("content");
                List<T> items = httpManager.convertList(content, typeRef);
                pageResult.setList(items);
            }
            
            if (response.containsKey("totalElements")) {
                pageResult.setTotal(Long.valueOf(response.get("totalElements").toString()));
            }
            
            if (response.containsKey("number")) {
                pageResult.setPageNum(Integer.valueOf(response.get("number").toString()) + 1);
            }
            
            if (response.containsKey("size")) {
                pageResult.setPageSize(Integer.valueOf(response.get("size").toString()));
            }
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("构建分页结果失败", e);
            return ResponseDTO.error("构建分页结果失败: " + e.getMessage());
        }
    }
}
