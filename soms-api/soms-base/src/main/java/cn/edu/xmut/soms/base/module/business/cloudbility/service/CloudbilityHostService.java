package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.config.CloudbilityConfig;
import cn.edu.xmut.soms.base.module.business.cloudbility.constant.CloudbilityConst;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.HostDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.HostImportForm;
import cn.edu.xmut.soms.base.module.business.cloudbility.manager.CloudbilityHttpManager;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家主机管理服务
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Service
public class CloudbilityHostService {

    @Resource
    private CloudbilityHttpManager httpManager;

    @Resource
    private CloudbilityConfig cloudbilityConfig;

    /**
     * 导入主机
     */
    public ResponseDTO<List<HostDTO>> importHosts(HostImportForm form) {
        try {
            Map<String, Object> params = buildHostImportParams(form);
            
            List<HostDTO> hosts = httpManager.post(CloudbilityConst.API_HOST + "/import", params, 
                new TypeReference<List<HostDTO>>() {});
            
            return ResponseDTO.ok(hosts);
        } catch (Exception e) {
            log.error("导入主机失败", e);
            return ResponseDTO.error("导入主机失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取主机详情
     */
    public ResponseDTO<HostDTO> getHostById(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            HostDTO hostDTO = httpManager.get(CloudbilityConst.API_HOST, params, 
                new TypeReference<HostDTO>() {});
            
            return ResponseDTO.ok(hostDTO);
        } catch (Exception e) {
            log.error("获取主机详情失败", e);
            return ResponseDTO.error("获取主机详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取主机列表
     */
    public ResponseDTO<PageResult<HostDTO>> getHostList(Integer page, Integer size, Long cloudId, 
                                                       String status, String platform, String keyword) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (page != null) {
                params.put("page", page);
            }
            if (size != null) {
                params.put("size", size);
            }
            if (cloudId != null) {
                params.put("cloudId", cloudId);
            }
            if (StringUtils.hasText(status)) {
                params.put("status", status);
            }
            if (StringUtils.hasText(platform)) {
                params.put("platform", platform);
            }
            if (StringUtils.hasText(keyword)) {
                params.put("keyword", keyword);
            }
            
            Map<String, Object> response = httpManager.get(CloudbilityConst.API_HOST, params,
                new TypeReference<Map<String, Object>>() {});
            
            return buildPageResult(response, new TypeReference<HostDTO>() {});
        } catch (Exception e) {
            log.error("获取主机列表失败", e);
            return ResponseDTO.error("获取主机列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新主机信息
     */
    public ResponseDTO<HostDTO> updateHost(Long hostId, Map<String, Object> updateData) {
        try {
            Map<String, Object> params = new HashMap<>(updateData);
            params.put("hostId", hostId);
            
            httpManager.put(CloudbilityConst.API_HOST, params, 
                new TypeReference<Map<String, Object>>() {});
            
            // 返回更新后的主机信息
            return getHostById(hostId);
        } catch (Exception e) {
            log.error("更新主机信息失败", e);
            return ResponseDTO.error("更新主机信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除主机
     */
    public ResponseDTO<Boolean> deleteHost(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.delete(CloudbilityConst.API_HOST, params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("删除主机失败", e);
            return ResponseDTO.error("删除主机失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除主机
     */
    public ResponseDTO<Boolean> deleteHosts(List<Long> hostIds) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostIds", String.join(",", hostIds.stream().map(String::valueOf).toArray(String[]::new)));
            
            httpManager.delete(CloudbilityConst.API_HOST + "/batch", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("批量删除主机失败", e);
            return ResponseDTO.error("批量删除主机失败: " + e.getMessage());
        }
    }

    /**
     * 启动主机
     */
    public ResponseDTO<Boolean> startHost(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/start", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("启动主机失败", e);
            return ResponseDTO.error("启动主机失败: " + e.getMessage());
        }
    }

    /**
     * 停止主机
     */
    public ResponseDTO<Boolean> stopHost(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/stop", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("停止主机失败", e);
            return ResponseDTO.error("停止主机失败: " + e.getMessage());
        }
    }

    /**
     * 重启主机
     */
    public ResponseDTO<Boolean> rebootHost(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/reboot", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("重启主机失败", e);
            return ResponseDTO.error("重启主机失败: " + e.getMessage());
        }
    }

    /**
     * 批量操作主机（启动/停止/重启）
     */
    public ResponseDTO<Boolean> batchOperateHosts(List<Long> hostIds, String operation) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostIds", String.join(",", hostIds.stream().map(String::valueOf).toArray(String[]::new)));
            params.put("operation", operation);
            
            httpManager.post(CloudbilityConst.API_HOST + "/batch/" + operation, params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("批量操作主机失败", e);
            return ResponseDTO.error("批量操作主机失败: " + e.getMessage());
        }
    }

    /**
     * 安装Agent
     */
    public ResponseDTO<Boolean> installAgent(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/agent/install", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("安装Agent失败", e);
            return ResponseDTO.error("安装Agent失败: " + e.getMessage());
        }
    }

    /**
     * 卸载Agent
     */
    public ResponseDTO<Boolean> uninstallAgent(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/agent/uninstall", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("卸载Agent失败", e);
            return ResponseDTO.error("卸载Agent失败: " + e.getMessage());
        }
    }

    /**
     * 获取主机监控数据
     */
    public ResponseDTO<Map<String, Object>> getHostMonitoring(Long hostId, String metricType, Long startTime, Long endTime) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            if (StringUtils.hasText(metricType)) {
                params.put("metricType", metricType);
            }
            if (startTime != null) {
                params.put("startTime", startTime);
            }
            if (endTime != null) {
                params.put("endTime", endTime);
            }
            
            Map<String, Object> monitoring = httpManager.get(CloudbilityConst.API_HOST + "/monitoring", params,
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(monitoring);
        } catch (Exception e) {
            log.error("获取主机监控数据失败", e);
            return ResponseDTO.error("获取主机监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 同步主机状态
     */
    public ResponseDTO<Boolean> syncHostStatus(Long hostId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostId", hostId);
            
            httpManager.post(CloudbilityConst.API_HOST + "/sync", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("同步主机状态失败", e);
            return ResponseDTO.error("同步主机状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步主机状态
     */
    public ResponseDTO<Boolean> batchSyncHostStatus(List<Long> hostIds) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("hostIds", String.join(",", hostIds.stream().map(String::valueOf).toArray(String[]::new)));
            
            httpManager.post(CloudbilityConst.API_HOST + "/sync/batch", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("批量同步主机状态失败", e);
            return ResponseDTO.error("批量同步主机状态失败: " + e.getMessage());
        }
    }

    /**
     * 构建主机导入参数
     */
    private Map<String, Object> buildHostImportParams(HostImportForm form) {
        Map<String, Object> params = new HashMap<>();
        
        if (form.getCloudId() != null) {
            params.put("cloudId", form.getCloudId());
        }
        if (StringUtils.hasText(form.getRegionId())) {
            params.put("regionId", form.getRegionId());
        }
        if (StringUtils.hasText(form.getZoneId())) {
            params.put("zoneId", form.getZoneId());
        }
        if (form.getInstanceIds() != null && !form.getInstanceIds().isEmpty()) {
            params.put("instanceIds", String.join(",", form.getInstanceIds()));
        }
        if (form.getTeamId() != null) {
            params.put("teamId", form.getTeamId());
        }
        if (form.getAutoInstallAgent() != null) {
            params.put("autoInstallAgent", form.getAutoInstallAgent());
        }
        
        return params;
    }

    /**
     * 构建分页结果
     */
    private <T> ResponseDTO<PageResult<T>> buildPageResult(Map<String, Object> response, TypeReference<T> typeRef) {
        try {
            PageResult<T> pageResult = new PageResult<>();
            
            if (response.containsKey("content")) {
                @SuppressWarnings("unchecked")
                List<Object> content = (List<Object>) response.get("content");
                List<T> items = httpManager.convertList(content, typeRef);
                pageResult.setList(items);
            }
            
            if (response.containsKey("totalElements")) {
                pageResult.setTotal(Long.valueOf(response.get("totalElements").toString()));
            }
            
            if (response.containsKey("number")) {
                pageResult.setPageNum(Integer.valueOf(response.get("number").toString()) + 1);
            }
            
            if (response.containsKey("size")) {
                pageResult.setPageSize(Integer.valueOf(response.get("size").toString()));
            }
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("构建分页结果失败", e);
            return ResponseDTO.error("构建分页结果失败: " + e.getMessage());
        }
    }
}
