# 行云管家（CloudBility）服务层使用说明

## 概述

本模块实现了与行云管家（CloudBility）平台的完整集成，支持用户、团队、云账户、主机等资源的管理操作。所有功能都通过标准的 REST API 调用实现，提供了统一的 service 层接口供其他业务模块调用。

## 核心组件

### 1. 配置类

- **CloudbilityConfig**: 配置 API 基础信息（baseUrl、accessKeyId、accessKeySecret、teamId 等）

### 2. 常量定义

- **CloudbilityConst**: 定义 API 路径、状态常量、厂商类型等

### 3. 数据传输对象 (DTO)

- **UserDTO**: 用户信息
- **TeamDTO**: 团队信息
- **CloudDTO**: 云账户信息
- **HostDTO**: 主机信息

### 4. 表单对象 (Form)

- **UserCreateForm**: 创建用户表单
- **TeamCreateForm**: 创建团队表单
- **CloudCreateForm**: 创建云账户表单
- **HostImportForm**: 导入主机表单

### 5. HTTP 管理器

- **CloudbilityHttpManager**: 封装 HTTP 请求，处理签名认证、参数处理等

### 6. 服务层

- **CloudbilityUserService**: 用户管理服务
- **CloudbilityTeamService**: 团队管理服务
- **CloudbilityCloudService**: 云账户管理服务
- **CloudbilityHostService**: 主机管理服务
- **CloudbilityService**: 综合服务（统一入口）

## 功能说明

### 用户管理

```java
@Resource
private CloudbilityService cloudbilityService;

// 创建用户
UserCreateForm form = new UserCreateForm();
form.setAccount("user001");
form.setName("测试用户");
form.setEmail("<EMAIL>");
ResponseDTO<UserDTO> result = cloudbilityService.createUser(form);

// 获取用户详情
ResponseDTO<UserDTO> user = cloudbilityService.getUserById(userId);

// 获取用户列表
ResponseDTO<PageResult<UserDTO>> users = cloudbilityService.getUserList(1, 10, "keyword");

// 锁定/解锁用户
cloudbilityService.lockUser(userId);
cloudbilityService.unlockUser(userId);

// 重置密码
ResponseDTO<String> newPassword = cloudbilityService.resetUserPassword(userId);
```

### 团队管理

```java
// 创建团队
TeamCreateForm form = new TeamCreateForm();
form.setTeamName("开发团队");
form.setTeamCode("dev_team");
ResponseDTO<TeamDTO> result = cloudbilityService.createTeam(form);

// 获取团队列表
ResponseDTO<PageResult<TeamDTO>> teams = cloudbilityService.getTeamList(1, 10, "dev");

// 添加团队成员
List<String> accounts = Arrays.asList("user1", "user2");
cloudbilityService.addTeamMembers(teamId, null, accounts);

// 获取团队成员
ResponseDTO<PageResult<Map<String, Object>>> members =
    cloudbilityService.getTeamMembers(teamId, 1, 10);
```

### 云账户管理

```java
// 创建云账户
CloudCreateForm form = new CloudCreateForm();
form.setName("生产环境VMware");
form.setProvider("vmware");
form.setAccessKeyId("your_key");
form.setAccessKeySecret("your_secret");
ResponseDTO<CloudDTO> result = cloudbilityService.createCloud(form);

// 获取云账户列表
ResponseDTO<PageResult<CloudDTO>> clouds =
    cloudbilityService.getCloudList(1, 10, "vmware", teamId, "keyword");

// 验证云账户连接
ResponseDTO<Boolean> isValid = cloudbilityService.validateCloudConnection(cloudId);

// 同步云账户资源
cloudbilityService.syncCloudResources(cloudId);

// 获取区域列表
ResponseDTO<List<Map<String, Object>>> regions =
    cloudbilityService.getCloudRegions(cloudId);
```

### 主机管理

```java
// 导入主机
HostImportForm form = new HostImportForm();
form.setCloudId(cloudId);
form.setInstanceIds(Arrays.asList("i-123", "i-456"));
form.setAutoInstallAgent(true);
ResponseDTO<List<HostDTO>> result = cloudbilityService.importHosts(form);

// 获取主机列表
ResponseDTO<PageResult<HostDTO>> hosts =
    cloudbilityService.getHostList(1, 10, cloudId, "Running", "linux", "web");

// 主机操作
cloudbilityService.startHost(hostId);      // 启动
cloudbilityService.stopHost(hostId);       // 停止
cloudbilityService.rebootHost(hostId);     // 重启

// 批量操作
List<Long> hostIds = Arrays.asList(1L, 2L, 3L);
cloudbilityService.batchOperateHosts(hostIds, "start");

// Agent 管理
cloudbilityService.installAgent(hostId);   // 安装Agent
cloudbilityService.uninstallAgent(hostId); // 卸载Agent

// 获取监控数据
ResponseDTO<Map<String, Object>> monitoring =
    cloudbilityService.getHostMonitoring(hostId, "cpu", startTime, endTime);
```

## 配置说明

在 `application.yaml` 中配置行云管家连接信息：

```yaml
cloudbility:
  base-url: https://base.xmut.edu.cn/api/openapi
  access-key-id: ${CLOUDBILITY_ACCESS_KEY_ID:your_access_key}
  access-key-secret: ${CLOUDBILITY_ACCESS_KEY_SECRET:your_access_secret}
  team-id: ${CLOUDBILITY_TEAM_ID:your_team_id}
  token-expire-seconds: 3600
  timeout: 30000
```

## 错误处理

所有服务方法都返回 `ResponseDTO<T>` 对象，包含：

- `ok`: 是否成功
- `data`: 返回数据
- `msg`: 错误消息

```java
ResponseDTO<UserDTO> result = cloudbilityService.getUserById(userId);
if (result.getOk()) {
    UserDTO user = result.getData();
    // 处理成功逻辑
} else {
    String errorMsg = result.getMsg();
    // 处理错误
}
```

## 注意事项

1. **认证机制**: 使用 HMAC-SHA256 签名算法进行 API 认证
2. **参数校验**: 创建表单对象包含必要的参数校验注解
3. **分页查询**: 支持标准的分页参数（page、size）和关键字搜索
4. **异常处理**: 统一封装异常处理，返回友好的错误信息
5. **日志记录**: 详细记录操作日志，便于问题排查

## 扩展说明

如需添加新的 API 接口：

1. 在 `CloudbilityConst` 中添加 API 路径常量
2. 根据需要创建对应的 DTO 和 Form 对象
3. 在相应的 Service 类中实现具体方法
4. 在 `CloudbilityService` 中添加统一入口方法

## 测试

提供了完整的测试类 `CloudbilityServiceTest`，覆盖了所有主要功能的测试用例。
