package cn.edu.xmut.soms.base.module.business.cloudbility.constant;

/**
 * 行云管家常量
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
public class CloudbilityConst {

    /**
     * API路径
     */
    public static final String API_USER = "/user";
    public static final String API_TEAM = "/team";
    public static final String API_CLOUD = "/cloud";
    public static final String API_HOST = "/host";
    public static final String API_PING = "/ping";

    /**
     * 用户操作路径
     */
    public static final String API_USER_LOCK = "/user/lock";
    public static final String API_USER_UNLOCK = "/user/unlock";
    public static final String API_USER_RESET_PASSWORD = "/user/resetPassword";
    public static final String API_USER_TEAMS = "/user/teams";

    /**
     * 团队操作路径
     */
    public static final String API_TEAM_MEMBER = "/team/member";

    /**
     * 云账户操作路径
     */
    public static final String API_CLOUD_VALIDATE = "/cloud/validate";
    public static final String API_CLOUD_SYNC = "/cloud/sync";
    public static final String API_CLOUD_REGION = "/cloud/region";
    public static final String API_CLOUD_ZONE = "/cloud/zone";

    /**
     * 主机操作路径
     */
    public static final String API_HOST_IMPORT = "/host/import";
    public static final String API_HOST_START = "/host/start";
    public static final String API_HOST_STOP = "/host/stop";
    public static final String API_HOST_REBOOT = "/host/reboot";
    public static final String API_HOST_BATCH = "/host/batch";
    public static final String API_HOST_AGENT_INSTALL = "/host/agent/install";
    public static final String API_HOST_AGENT_UNINSTALL = "/host/agent/uninstall";
    public static final String API_HOST_MONITORING = "/host/monitoring";
    public static final String API_HOST_SYNC = "/host/sync";

    /**
     * 云厂商类型
     */
    public static final String PROVIDER_VMWARE = "vmware";
    public static final String PROVIDER_PRIVATE = "private";
    public static final String PROVIDER_ALIYUN = "aliyun";
    public static final String PROVIDER_QCLOUD = "qcloud";

    /**
     * 主机状态
     */
    public static final String HOST_STATUS_RUNNING = "Running";
    public static final String HOST_STATUS_STOPPED = "Stopped";
    public static final String HOST_STATUS_UNKNOWN = "Unknown";

    /**
     * 用户状态
     */
    public static final String USER_STATUS_ACTIVE = "ACTIVE";
    public static final String USER_STATUS_INACTIVE = "INACTIVE";
    public static final String USER_STATUS_BLOCK = "BLOCK";

    /**
     * 团队类型
     */
    public static final String TEAM_TYPE_BASIC = "BASIC";
    public static final String TEAM_TYPE_PRO = "PRO";
    public static final String TEAM_TYPE_ENT = "ENT";

    /**
     * 云账户内网访问类型
     */
    public static final String INTRANET_ACCESS_DIRECTLY = "directly";
    public static final String INTRANET_ACCESS_PROXY = "proxy";
}
