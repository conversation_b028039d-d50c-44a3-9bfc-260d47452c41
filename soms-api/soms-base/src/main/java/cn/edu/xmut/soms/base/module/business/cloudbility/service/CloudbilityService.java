package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.CloudDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.HostDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.TeamDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.UserDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 行云管家综合服务
 * 提供统一的入口调用其他业务模块
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Service
public class CloudbilityService {

    @Resource
    private CloudbilityUserService userService;

    @Resource
    private CloudbilityTeamService teamService;

    @Resource
    private CloudbilityCloudService cloudService;

    @Resource
    private CloudbilityHostService hostService;

    // ==================== 用户管理 ====================

    /**
     * 创建用户
     */
    public ResponseDTO<UserDTO> createUser(UserCreateForm form) {
        return userService.createUser(form);
    }

    /**
     * 获取用户详情
     */
    public ResponseDTO<UserDTO> getUserById(Long userId) {
        return userService.getUserById(userId);
    }

    /**
     * 获取用户列表
     */
    public ResponseDTO<PageResult<UserDTO>> getUserList(Integer page, Integer size, String keyword) {
        return userService.getUserList(page, size, keyword);
    }

    /**
     * 更新用户信息
     */
    public ResponseDTO<UserDTO> updateUser(Long userId, UserCreateForm form) {
        return userService.updateUser(userId, form);
    }

    /**
     * 删除用户
     */
    public ResponseDTO<Boolean> deleteUser(Long userId) {
        return userService.deleteUser(userId);
    }

    /**
     * 锁定用户
     */
    public ResponseDTO<Boolean> lockUser(Long userId) {
        return userService.lockUser(userId);
    }

    /**
     * 解锁用户
     */
    public ResponseDTO<Boolean> unlockUser(Long userId) {
        return userService.unlockUser(userId);
    }

    /**
     * 重置用户密码
     */
    public ResponseDTO<String> resetUserPassword(Long userId) {
        return userService.resetPassword(userId);
    }

    /**
     * 获取用户所属团队
     */
    public ResponseDTO<List<TeamDTO>> getUserTeams(Long userId) {
        return userService.getUserTeams(userId);
    }

    // ==================== 团队管理 ====================

    /**
     * 创建团队
     */
    public ResponseDTO<TeamDTO> createTeam(TeamCreateForm form) {
        return teamService.createTeam(form);
    }

    /**
     * 获取团队详情
     */
    public ResponseDTO<TeamDTO> getTeamById(Long teamId) {
        return teamService.getTeamById(teamId);
    }

    /**
     * 获取团队列表
     */
    public ResponseDTO<PageResult<TeamDTO>> getTeamList(Integer page, Integer size, String keyword) {
        return teamService.getTeamList(page, size, keyword);
    }

    /**
     * 更新团队信息
     */
    public ResponseDTO<TeamDTO> updateTeam(Long teamId, TeamCreateForm form) {
        return teamService.updateTeam(teamId, form);
    }

    /**
     * 删除团队
     */
    public ResponseDTO<Boolean> deleteTeam(Long teamId) {
        return teamService.deleteTeam(teamId);
    }

    /**
     * 添加团队成员
     */
    public ResponseDTO<Boolean> addTeamMembers(Long teamId, List<Long> memberIds, List<String> memberAccounts) {
        return teamService.addTeamMembers(teamId, memberIds, memberAccounts);
    }

    /**
     * 移除团队成员
     */
    public ResponseDTO<Boolean> removeTeamMembers(Long teamId, List<Long> memberIds, List<String> memberAccounts) {
        return teamService.removeTeamMembers(teamId, memberIds, memberAccounts);
    }

    /**
     * 获取团队成员列表
     */
    public ResponseDTO<PageResult<Map<String, Object>>> getTeamMembers(Long teamId, Integer page, Integer size) {
        return teamService.getTeamMembers(teamId, page, size);
    }

    // ==================== 云账户管理 ====================

    /**
     * 创建云账户
     */
    public ResponseDTO<CloudDTO> createCloud(CloudCreateForm form) {
        return cloudService.createCloud(form);
    }

    /**
     * 获取云账户详情
     */
    public ResponseDTO<CloudDTO> getCloudById(Long cloudId) {
        return cloudService.getCloudById(cloudId);
    }

    /**
     * 获取云账户列表
     */
    public ResponseDTO<PageResult<CloudDTO>> getCloudList(Integer page, Integer size, String provider, Long teamId, String keyword) {
        return cloudService.getCloudList(page, size, provider, teamId, keyword);
    }

    /**
     * 更新云账户信息
     */
    public ResponseDTO<CloudDTO> updateCloud(Long cloudId, CloudCreateForm form) {
        return cloudService.updateCloud(cloudId, form);
    }

    /**
     * 删除云账户
     */
    public ResponseDTO<Boolean> deleteCloud(Long cloudId) {
        return cloudService.deleteCloud(cloudId);
    }

    /**
     * 验证云账户连接
     */
    public ResponseDTO<Boolean> validateCloudConnection(Long cloudId) {
        return cloudService.validateCloudConnection(cloudId);
    }

    /**
     * 同步云账户资源
     */
    public ResponseDTO<Boolean> syncCloudResources(Long cloudId) {
        return cloudService.syncCloudResources(cloudId);
    }

    /**
     * 获取云账户的区域列表
     */
    public ResponseDTO<List<Map<String, Object>>> getCloudRegions(Long cloudId) {
        return cloudService.getCloudRegions(cloudId);
    }

    /**
     * 获取云账户的可用区列表
     */
    public ResponseDTO<List<Map<String, Object>>> getCloudZones(Long cloudId, String regionId) {
        return cloudService.getCloudZones(cloudId, regionId);
    }

    // ==================== 主机管理 ====================

    /**
     * 导入主机
     */
    public ResponseDTO<List<HostDTO>> importHosts(HostImportForm form) {
        return hostService.importHosts(form);
    }

    /**
     * 获取主机详情
     */
    public ResponseDTO<HostDTO> getHostById(Long hostId) {
        return hostService.getHostById(hostId);
    }

    /**
     * 获取主机列表
     */
    public ResponseDTO<PageResult<HostDTO>> getHostList(Integer page, Integer size, Long cloudId, 
                                                       String status, String platform, String keyword) {
        return hostService.getHostList(page, size, cloudId, status, platform, keyword);
    }

    /**
     * 更新主机信息
     */
    public ResponseDTO<HostDTO> updateHost(Long hostId, Map<String, Object> updateData) {
        return hostService.updateHost(hostId, updateData);
    }

    /**
     * 删除主机
     */
    public ResponseDTO<Boolean> deleteHost(Long hostId) {
        return hostService.deleteHost(hostId);
    }

    /**
     * 批量删除主机
     */
    public ResponseDTO<Boolean> deleteHosts(List<Long> hostIds) {
        return hostService.deleteHosts(hostIds);
    }

    /**
     * 启动主机
     */
    public ResponseDTO<Boolean> startHost(Long hostId) {
        return hostService.startHost(hostId);
    }

    /**
     * 停止主机
     */
    public ResponseDTO<Boolean> stopHost(Long hostId) {
        return hostService.stopHost(hostId);
    }

    /**
     * 重启主机
     */
    public ResponseDTO<Boolean> rebootHost(Long hostId) {
        return hostService.rebootHost(hostId);
    }

    /**
     * 批量操作主机（启动/停止/重启）
     */
    public ResponseDTO<Boolean> batchOperateHosts(List<Long> hostIds, String operation) {
        return hostService.batchOperateHosts(hostIds, operation);
    }

    /**
     * 安装Agent
     */
    public ResponseDTO<Boolean> installAgent(Long hostId) {
        return hostService.installAgent(hostId);
    }

    /**
     * 卸载Agent
     */
    public ResponseDTO<Boolean> uninstallAgent(Long hostId) {
        return hostService.uninstallAgent(hostId);
    }

    /**
     * 获取主机监控数据
     */
    public ResponseDTO<Map<String, Object>> getHostMonitoring(Long hostId, String metricType, Long startTime, Long endTime) {
        return hostService.getHostMonitoring(hostId, metricType, startTime, endTime);
    }

    /**
     * 同步主机状态
     */
    public ResponseDTO<Boolean> syncHostStatus(Long hostId) {
        return hostService.syncHostStatus(hostId);
    }

    /**
     * 批量同步主机状态
     */
    public ResponseDTO<Boolean> batchSyncHostStatus(List<Long> hostIds) {
        return hostService.batchSyncHostStatus(hostIds);
    }
}
