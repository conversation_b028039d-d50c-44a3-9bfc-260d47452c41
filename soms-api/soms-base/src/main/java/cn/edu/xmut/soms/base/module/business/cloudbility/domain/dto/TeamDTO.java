package cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 团队信息DTO
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeamDTO extends CloudbilityBaseResponse {

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 团队标识
     */
    private String code;

    /**
     * 团队名称
     */
    private String name;

    /**
     * 团队类型
     */
    private String teamType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 团队创建者ID
     */
    private Long creator;

    /**
     * 是否开启了组织架构视图
     */
    private Boolean departmentEnabled;

    /**
     * 配额是否超限
     */
    private Boolean quotaExceed;

    /**
     * 专业版到期时间
     */
    private LocalDateTime serveEndTime;

    /**
     * 团队成员ID列表
     */
    private List<Long> members;
}
