package cn.edu.xmut.soms.base.module.business.cloudbility.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 导入主机表单
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@Schema(description = "导入主机表单")
public class HostImportForm {

    /**
     * 局域网云账户ID
     */
    @Schema(description = "局域网云账户ID")
    @NotNull(message = "云账户ID不能为空")
    private Long cloudId;

    /**
     * 内网IP
     */
    @Schema(description = "内网IP")
    @NotBlank(message = "内网IP不能为空")
    private String innerIp;

    /**
     * 公网IP
     */
    @Schema(description = "公网IP")
    private String publicIp;

    /**
     * 主机实例ID
     */
    @Schema(description = "主机实例ID")
    private String instanceId;

    /**
     * 主机名称
     */
    @Schema(description = "主机名称")
    private String name;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统")
    private String operatingSystem;

    /**
     * CPU核数
     */
    @Schema(description = "CPU核数")
    private Integer processor;

    /**
     * 内存（MB）
     */
    @Schema(description = "内存（MB）")
    private Integer memory;

    /**
     * 主机状态
     */
    @Schema(description = "主机状态")
    private String status;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID")
    private Long creatorId;

    /**
     * 虚拟分组ID列表
     */
    @Schema(description = "虚拟分组ID列表")
    private String vgIds;
}
