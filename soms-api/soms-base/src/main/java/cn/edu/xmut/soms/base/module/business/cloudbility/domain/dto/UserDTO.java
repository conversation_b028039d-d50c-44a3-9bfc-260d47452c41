package cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户信息DTO
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends CloudbilityBaseResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 用户昵称
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别 1-男 2-女
     */
    private Integer gender;

    /**
     * 地址
     */
    private String address;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 认证方式
     */
    private String authMethod;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 用户UID
     */
    private String uid;
}
