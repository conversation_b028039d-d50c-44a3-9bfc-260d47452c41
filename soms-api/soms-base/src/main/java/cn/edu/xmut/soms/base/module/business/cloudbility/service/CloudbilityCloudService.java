package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.config.CloudbilityConfig;
import cn.edu.xmut.soms.base.module.business.cloudbility.constant.CloudbilityConst;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.CloudDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.CloudCreateForm;
import cn.edu.xmut.soms.base.module.business.cloudbility.manager.CloudbilityHttpManager;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家云账户管理服务
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Service
public class CloudbilityCloudService {

    @Resource
    private CloudbilityHttpManager httpManager;

    @Resource
    private CloudbilityConfig cloudbilityConfig;

    /**
     * 创建云账户
     */
    public ResponseDTO<CloudDTO> createCloud(CloudCreateForm form) {
        try {
            Map<String, Object> params = buildCloudCreateParams(form);
            
            Map<String, Object> response = httpManager.post(CloudbilityConst.API_CLOUD, params, 
                new TypeReference<Map<String, Object>>() {});
            
            if (response.containsKey("id")) {
                // 创建成功，获取云账户详情
                Long cloudId = Long.valueOf(response.get("id").toString());
                return getCloudById(cloudId);
            } else {
                return ResponseDTO.error("创建云账户失败");
            }
        } catch (Exception e) {
            log.error("创建云账户失败", e);
            return ResponseDTO.error("创建云账户失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取云账户详情
     */
    public ResponseDTO<CloudDTO> getCloudById(Long cloudId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            
            CloudDTO cloudDTO = httpManager.get(CloudbilityConst.API_CLOUD, params, 
                new TypeReference<CloudDTO>() {});
            
            return ResponseDTO.ok(cloudDTO);
        } catch (Exception e) {
            log.error("获取云账户详情失败", e);
            return ResponseDTO.error("获取云账户详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取云账户列表
     */
    public ResponseDTO<PageResult<CloudDTO>> getCloudList(Integer page, Integer size, String provider, Long teamId, String keyword) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (page != null) {
                params.put("page", page);
            }
            if (size != null) {
                params.put("size", size);
            }
            if (StringUtils.hasText(provider)) {
                params.put("provider", provider);
            }
            if (teamId != null) {
                params.put("teamId", teamId);
            }
            if (StringUtils.hasText(keyword)) {
                params.put("keyword", keyword);
            }
            
            Map<String, Object> response = httpManager.get(CloudbilityConst.API_CLOUD, params,
                new TypeReference<Map<String, Object>>() {});
            
            return buildPageResult(response, new TypeReference<CloudDTO>() {});
        } catch (Exception e) {
            log.error("获取云账户列表失败", e);
            return ResponseDTO.error("获取云账户列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新云账户信息
     */
    public ResponseDTO<CloudDTO> updateCloud(Long cloudId, CloudCreateForm form) {
        try {
            Map<String, Object> params = buildCloudCreateParams(form);
            params.put("id", cloudId);
            
            httpManager.put(CloudbilityConst.API_CLOUD, params, 
                new TypeReference<Map<String, Object>>() {});
            
            // 返回更新后的云账户信息
            return getCloudById(cloudId);
        } catch (Exception e) {
            log.error("更新云账户信息失败", e);
            return ResponseDTO.error("更新云账户信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除云账户
     */
    public ResponseDTO<Boolean> deleteCloud(Long cloudId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            
            httpManager.delete(CloudbilityConst.API_CLOUD, params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("删除云账户失败", e);
            return ResponseDTO.error("删除云账户失败: " + e.getMessage());
        }
    }

    /**
     * 验证云账户连接
     */
    public ResponseDTO<Boolean> validateCloudConnection(Long cloudId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            
            Map<String, Object> response = httpManager.post(CloudbilityConst.API_CLOUD + "/validate", params, 
                new TypeReference<Map<String, Object>>() {});
            
            boolean isValid = Boolean.parseBoolean(response.getOrDefault("valid", false).toString());
            return ResponseDTO.ok(isValid);
        } catch (Exception e) {
            log.error("验证云账户连接失败", e);
            return ResponseDTO.error("验证云账户连接失败: " + e.getMessage());
        }
    }

    /**
     * 同步云账户资源
     */
    public ResponseDTO<Boolean> syncCloudResources(Long cloudId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            
            httpManager.post(CloudbilityConst.API_CLOUD + "/sync", params, 
                new TypeReference<Map<String, Object>>() {});
            
            return ResponseDTO.ok(true);
        } catch (Exception e) {
            log.error("同步云账户资源失败", e);
            return ResponseDTO.error("同步云账户资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取云账户的区域列表
     */
    public ResponseDTO<List<Map<String, Object>>> getCloudRegions(Long cloudId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            
            List<Map<String, Object>> regions = httpManager.get(CloudbilityConst.API_CLOUD + "/region", params,
                new TypeReference<List<Map<String, Object>>>() {});
            
            return ResponseDTO.ok(regions);
        } catch (Exception e) {
            log.error("获取云账户区域列表失败", e);
            return ResponseDTO.error("获取云账户区域列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取云账户的可用区列表
     */
    public ResponseDTO<List<Map<String, Object>>> getCloudZones(Long cloudId, String regionId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("id", cloudId);
            if (StringUtils.hasText(regionId)) {
                params.put("regionId", regionId);
            }
            
            List<Map<String, Object>> zones = httpManager.get(CloudbilityConst.API_CLOUD + "/zone", params,
                new TypeReference<List<Map<String, Object>>>() {});
            
            return ResponseDTO.ok(zones);
        } catch (Exception e) {
            log.error("获取云账户可用区列表失败", e);
            return ResponseDTO.error("获取云账户可用区列表失败: " + e.getMessage());
        }
    }

    /**
     * 构建云账户创建参数
     */
    private Map<String, Object> buildCloudCreateParams(CloudCreateForm form) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(form.getName())) {
            params.put("name", form.getName());
        }
        if (StringUtils.hasText(form.getProvider())) {
            params.put("provider", form.getProvider());
        }
        if (StringUtils.hasText(form.getAccountId())) {
            params.put("accountId", form.getAccountId());
        }
        if (StringUtils.hasText(form.getAccessKeyId())) {
            params.put("accessKeyId", form.getAccessKeyId());
        }
        if (StringUtils.hasText(form.getAccessKeySecret())) {
            params.put("accessKeySecret", form.getAccessKeySecret());
        }
        if (form.getTeamId() != null) {
            params.put("teamId", form.getTeamId());
        }
        if (StringUtils.hasText(form.getCloudIntranetAccessType())) {
            params.put("cloudIntranetAccessType", form.getCloudIntranetAccessType());
        }
        if (StringUtils.hasText(form.getRegionId())) {
            params.put("regionId", form.getRegionId());
        }
        if (StringUtils.hasText(form.getDescription())) {
            params.put("description", form.getDescription());
        }
        
        return params;
    }

    /**
     * 构建分页结果
     */
    private <T> ResponseDTO<PageResult<T>> buildPageResult(Map<String, Object> response, TypeReference<T> typeRef) {
        try {
            PageResult<T> pageResult = new PageResult<>();
            
            if (response.containsKey("content")) {
                @SuppressWarnings("unchecked")
                List<Object> content = (List<Object>) response.get("content");
                List<T> items = httpManager.convertList(content, typeRef);
                pageResult.setList(items);
            }
            
            if (response.containsKey("totalElements")) {
                pageResult.setTotal(Long.valueOf(response.get("totalElements").toString()));
            }
            
            if (response.containsKey("number")) {
                pageResult.setPageNum(Integer.valueOf(response.get("number").toString()) + 1);
            }
            
            if (response.containsKey("size")) {
                pageResult.setPageSize(Integer.valueOf(response.get("size").toString()));
            }
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("构建分页结果失败", e);
            return ResponseDTO.error("构建分页结果失败: " + e.getMessage());
        }
    }
}
