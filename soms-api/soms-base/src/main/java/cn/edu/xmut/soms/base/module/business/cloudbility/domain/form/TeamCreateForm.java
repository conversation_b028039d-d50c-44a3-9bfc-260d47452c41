package cn.edu.xmut.soms.base.module.business.cloudbility.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建团队表单
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@Schema(description = "创建团队表单")
public class TeamCreateForm {

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    @NotBlank(message = "团队名称不能为空")
    private String teamName;

    /**
     * 团队标识
     */
    @Schema(description = "团队标识")
    private String teamCode;

    /**
     * 团队创建者用户ID
     */
    @Schema(description = "团队创建者用户ID")
    private Long creatorId;

    /**
     * 团队创建者账号
     */
    @Schema(description = "团队创建者账号")
    private String creatorAccount;

    /**
     * 团队成员配额
     */
    @Schema(description = "团队成员配额")
    private Integer memberQuota;

    /**
     * 团队成员ID列表
     */
    @Schema(description = "团队成员ID列表")
    private String members;

    /**
     * 团队成员账号列表
     */
    @Schema(description = "团队成员账号列表")
    private String memberAccounts;
}
