package cn.edu.xmut.soms.base.module.business.cloudbility.manager;

import cn.edu.xmut.soms.base.module.business.cloudbility.config.CloudbilityConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.Map;
import java.util.TreeMap;

/**
 * 行云管家HTTP客户端
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Slf4j
@Component
public class CloudbilityHttpManager {

    @Resource
    private CloudbilityConfig cloudbilityConfig;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 发送GET请求
     */
    public <T> T get(String path, Map<String, Object> params, TypeReference<T> responseType) {
        try {
            String url = buildUrl(path, params);
            HttpHeaders headers = buildHeaders("GET", path, params);
            HttpEntity<?> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            return parseResponse(response.getBody(), responseType);
        } catch (Exception e) {
            log.error("CloudBility GET请求失败: path={}, params={}", path, params, e);
            throw new RuntimeException("CloudBility API调用失败", e);
        }
    }

    /**
     * 发送POST请求
     */
    public <T> T post(String path, Map<String, Object> params, TypeReference<T> responseType) {
        try {
            String url = cloudbilityConfig.getBaseUrl() + path;
            HttpHeaders headers = buildHeaders("POST", path, params);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            if (params != null) {
                params.forEach((key, value) -> body.add(key, value));
            }
            
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            return parseResponse(response.getBody(), responseType);
        } catch (Exception e) {
            log.error("CloudBility POST请求失败: path={}, params={}", path, params, e);
            throw new RuntimeException("CloudBility API调用失败", e);
        }
    }

    /**
     * 发送PUT请求
     */
    public <T> T put(String path, Map<String, Object> params, TypeReference<T> responseType) {
        try {
            String url = buildUrl(path, params);
            HttpHeaders headers = buildHeaders("PUT", path, params);
            HttpEntity<?> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
            return parseResponse(response.getBody(), responseType);
        } catch (Exception e) {
            log.error("CloudBility PUT请求失败: path={}, params={}", path, params, e);
            throw new RuntimeException("CloudBility API调用失败", e);
        }
    }

    /**
     * 发送DELETE请求
     */
    public <T> T delete(String path, Map<String, Object> params, TypeReference<T> responseType) {
        try {
            String url = buildUrl(path, params);
            HttpHeaders headers = buildHeaders("DELETE", path, params);
            HttpEntity<?> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);
            return parseResponse(response.getBody(), responseType);
        } catch (Exception e) {
            log.error("CloudBility DELETE请求失败: path={}, params={}", path, params, e);
            throw new RuntimeException("CloudBility API调用失败", e);
        }
    }

    /**
     * 构建URL
     */
    private String buildUrl(String path, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(cloudbilityConfig.getBaseUrl() + path);
        if (params != null) {
            params.forEach((key, value) -> {
                if (value != null) {
                    builder.queryParam(key, value);
                }
            });
        }
        return builder.toUriString();
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildHeaders(String method, String path, Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        // 添加认证头
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String nonce = java.util.UUID.randomUUID().toString().replace("-", "");
        String signature = generateSignature(method, path, params, timestamp, nonce);
        
        headers.add("accessKeyId", cloudbilityConfig.getAccessKeyId());
        headers.add("timestamp", timestamp);
        headers.add("nonce", nonce);
        headers.add("signature", signature);
        
        return headers;
    }

    /**
     * 生成签名
     */
    private String generateSignature(String method, String path, Map<String, Object> params, 
                                   String timestamp, String nonce) {
        try {
            // 构建待签名字符串
            StringBuilder sb = new StringBuilder();
            sb.append(method.toUpperCase()).append("\n");
            sb.append(path).append("\n");
            
            // 参数排序
            Map<String, Object> sortedParams = new TreeMap<>();
            if (params != null) {
                sortedParams.putAll(params);
            }
            sortedParams.put("accessKeyId", cloudbilityConfig.getAccessKeyId());
            sortedParams.put("timestamp", timestamp);
            sortedParams.put("nonce", nonce);
            
            // 构建查询字符串
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }
            sb.append(queryString);

            // HMAC-SHA256签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                cloudbilityConfig.getAccessKeySecret().getBytes(StandardCharsets.UTF_8), 
                "HmacSHA256"
            );
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(sb.toString().getBytes(StandardCharsets.UTF_8));
            
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 解析响应
     */
    private <T> T parseResponse(String responseBody, TypeReference<T> responseType) {
        try {
            return objectMapper.readValue(responseBody, responseType);
        } catch (Exception e) {
            log.error("解析响应失败: {}", responseBody, e);
            throw new RuntimeException("解析响应失败", e);
        }
    }

    /**
     * 转换列表数据
     */
    public <T> List<T> convertList(List<Object> sourceList, TypeReference<T> targetType) {
        try {
            List<T> resultList = new ArrayList<>();
            for (Object item : sourceList) {
                String json = objectMapper.writeValueAsString(item);
                T convertedItem = objectMapper.readValue(json, targetType);
                resultList.add(convertedItem);
            }
            return resultList;
        } catch (Exception e) {
            log.error("转换列表数据失败", e);
            throw new RuntimeException("转换列表数据失败", e);
        }
    }
}
