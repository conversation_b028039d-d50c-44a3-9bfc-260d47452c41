package cn.edu.xmut.soms.base.module.business.cloudbility.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 行云管家配置
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@Component
@ConfigurationProperties(prefix = "cloudbility")
public class CloudbilityConfig {

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * Access Key ID
     */
    private String accessKeyId;

    /**
     * Access Key Secret
     */
    private String accessKeySecret;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * Token过期时间（秒）
     */
    private Integer tokenExpireSeconds;

    /**
     * 超时时间
     */
    private Integer timeout;
}
