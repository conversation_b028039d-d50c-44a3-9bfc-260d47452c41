package cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 云账户信息DTO
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CloudDTO extends CloudbilityBaseResponse {

    /**
     * 云账户ID
     */
    private Long id;

    /**
     * 云账户名称
     */
    private String name;

    /**
     * 云账户厂商
     */
    private String provider;

    /**
     * 账号ID
     */
    private String accountId;

    /**
     * 所属团队ID
     */
    private Long teamId;

    /**
     * 云账户来源
     */
    private String scope;

    /**
     * 内网连接方式
     */
    private String cloudIntranetAccessType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
