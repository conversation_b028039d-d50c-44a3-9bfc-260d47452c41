package cn.edu.xmut.soms.base.module.business.cloudbility.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建云账户表单
 *
 * <AUTHOR>
 * @Date 2025-07-09
 */
@Data
@Schema(description = "创建云账户表单")
public class CloudCreateForm {

    /**
     * 所属团队ID
     */
    @Schema(description = "所属团队ID")
    @NotNull(message = "所属团队ID不能为空")
    private Long teamId;

    /**
     * 云账户名称
     */
    @Schema(description = "云账户名称")
    @NotBlank(message = "云账户名称不能为空")
    private String name;

    /**
     * 创建者用户ID
     */
    @Schema(description = "创建者用户ID")
    private Long creatorId;

    /**
     * 角色权限
     */
    @Schema(description = "角色权限")
    private String roles;

    /**
     * 内网连接方式
     */
    @Schema(description = "内网连接方式")
    @NotBlank(message = "内网连接方式不能为空")
    private String intranetAccessType;

    /**
     * 网段划分方式
     */
    @Schema(description = "网段划分方式")
    private String cidrRule;

    /**
     * CIDR网段
     */
    @Schema(description = "CIDR网段")
    private String cidrs;

    /**
     * 参考运维子策略ID
     */
    @Schema(description = "参考运维子策略ID")
    private Long referencedPolicyId;
}
