package cn.edu.xmut.soms.base.module.business.cloudbility.service;

import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.CloudDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.HostDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.TeamDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.dto.UserDTO;
import cn.edu.xmut.soms.base.module.business.cloudbility.domain.form.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家服务测试类
 * 
 * <AUTHOR>
 * @Date 2025-07-09
 */
@SpringBootTest
@ActiveProfiles("dev")
public class CloudbilityServiceTest {

    @Resource
    private CloudbilityService cloudbilityService;

    /**
     * 测试用户管理功能
     */
    @Test
    public void testUserManagement() {
        // 创建用户
        UserCreateForm createForm = new UserCreateForm();
        createForm.setAccount("test_user");
        createForm.setName("测试用户");
        createForm.setEmail("<EMAIL>");
        createForm.setPhone("***********");
        createForm.setTeamId(182826779545600L);
        
        ResponseDTO<UserDTO> createResult = cloudbilityService.createUser(createForm);
        System.out.println("创建用户结果: " + createResult);
        
        if (createResult.getOk()) {
            Long userId = createResult.getData().getUserId();
            
            // 获取用户详情
            ResponseDTO<UserDTO> userDetail = cloudbilityService.getUserById(userId);
            System.out.println("用户详情: " + userDetail);
            
            // 获取用户列表
            ResponseDTO<PageResult<UserDTO>> userList = cloudbilityService.getUserList(1, 10, "test");
            System.out.println("用户列表: " + userList);
            
            // 锁定用户
            ResponseDTO<Boolean> lockResult = cloudbilityService.lockUser(userId);
            System.out.println("锁定用户结果: " + lockResult);
            
            // 解锁用户
            ResponseDTO<Boolean> unlockResult = cloudbilityService.unlockUser(userId);
            System.out.println("解锁用户结果: " + unlockResult);
            
            // 重置密码
            ResponseDTO<String> resetResult = cloudbilityService.resetUserPassword(userId);
            System.out.println("重置密码结果: " + resetResult);
            
            // 获取用户所属团队
            ResponseDTO<List<TeamDTO>> userTeams = cloudbilityService.getUserTeams(userId);
            System.out.println("用户团队: " + userTeams);
        }
    }

    /**
     * 测试团队管理功能
     */
    @Test
    public void testTeamManagement() {
        // 创建团队
        TeamCreateForm createForm = new TeamCreateForm();
        createForm.setTeamName("测试团队");
        createForm.setTeamCode("test_team");
        createForm.setCreatorAccount("admin");
        createForm.setMemberQuota(100);
        
        ResponseDTO<TeamDTO> createResult = cloudbilityService.createTeam(createForm);
        System.out.println("创建团队结果: " + createResult);
        
        if (createResult.getOk()) {
            Long teamId = createResult.getData().getTeamId();
            
            // 获取团队详情
            ResponseDTO<TeamDTO> teamDetail = cloudbilityService.getTeamById(teamId);
            System.out.println("团队详情: " + teamDetail);
            
            // 获取团队列表
            ResponseDTO<PageResult<TeamDTO>> teamList = cloudbilityService.getTeamList(1, 10, "测试");
            System.out.println("团队列表: " + teamList);
            
            // 添加团队成员
            List<String> memberAccounts = Arrays.asList("user1", "user2");
            ResponseDTO<Boolean> addMemberResult = cloudbilityService.addTeamMembers(teamId, null, memberAccounts);
            System.out.println("添加团队成员结果: " + addMemberResult);
            
            // 获取团队成员列表
            ResponseDTO<PageResult<Map<String, Object>>> memberList = cloudbilityService.getTeamMembers(teamId, 1, 10);
            System.out.println("团队成员列表: " + memberList);
        }
    }

    /**
     * 测试云账户管理功能
     */
    @Test
    public void testCloudManagement() {
        // 创建云账户
        CloudCreateForm createForm = new CloudCreateForm();
        createForm.setName("测试云账户");
        createForm.setProvider("vmware");
        createForm.setAccountId("test_account");
        createForm.setAccessKeyId("test_key");
        createForm.setAccessKeySecret("test_secret");
        createForm.setTeamId(182826779545600L);
        
        ResponseDTO<CloudDTO> createResult = cloudbilityService.createCloud(createForm);
        System.out.println("创建云账户结果: " + createResult);
        
        if (createResult.getOk()) {
            Long cloudId = createResult.getData().getId();
            
            // 获取云账户详情
            ResponseDTO<CloudDTO> cloudDetail = cloudbilityService.getCloudById(cloudId);
            System.out.println("云账户详情: " + cloudDetail);
            
            // 获取云账户列表
            ResponseDTO<PageResult<CloudDTO>> cloudList = cloudbilityService.getCloudList(1, 10, "vmware", null, "测试");
            System.out.println("云账户列表: " + cloudList);
            
            // 验证云账户连接
            ResponseDTO<Boolean> validateResult = cloudbilityService.validateCloudConnection(cloudId);
            System.out.println("验证云账户连接结果: " + validateResult);
            
            // 获取云账户区域列表
            ResponseDTO<List<Map<String, Object>>> regionList = cloudbilityService.getCloudRegions(cloudId);
            System.out.println("云账户区域列表: " + regionList);
            
            // 同步云账户资源
            ResponseDTO<Boolean> syncResult = cloudbilityService.syncCloudResources(cloudId);
            System.out.println("同步云账户资源结果: " + syncResult);
        }
    }

    /**
     * 测试主机管理功能
     */
    @Test
    public void testHostManagement() {
        // 导入主机
        HostImportForm importForm = new HostImportForm();
        importForm.setCloudId(1L);
        importForm.setRegionId("cn-hangzhou");
        importForm.setInstanceIds(Arrays.asList("i-test1", "i-test2"));
        importForm.setTeamId(182826779545600L);
        importForm.setAutoInstallAgent(true);
        
        ResponseDTO<List<HostDTO>> importResult = cloudbilityService.importHosts(importForm);
        System.out.println("导入主机结果: " + importResult);
        
        // 获取主机列表
        ResponseDTO<PageResult<HostDTO>> hostList = cloudbilityService.getHostList(1, 10, null, "Running", null, "test");
        System.out.println("主机列表: " + hostList);
        
        if (hostList.getOk() && hostList.getData().getList() != null && !hostList.getData().getList().isEmpty()) {
            Long hostId = hostList.getData().getList().get(0).getHostId();
            
            // 获取主机详情
            ResponseDTO<HostDTO> hostDetail = cloudbilityService.getHostById(hostId);
            System.out.println("主机详情: " + hostDetail);
            
            // 启动主机
            ResponseDTO<Boolean> startResult = cloudbilityService.startHost(hostId);
            System.out.println("启动主机结果: " + startResult);
            
            // 获取主机监控数据
            ResponseDTO<Map<String, Object>> monitoringResult = cloudbilityService.getHostMonitoring(
                hostId, "cpu", System.currentTimeMillis() - 3600000, System.currentTimeMillis());
            System.out.println("主机监控数据: " + monitoringResult);
            
            // 安装Agent
            ResponseDTO<Boolean> installAgentResult = cloudbilityService.installAgent(hostId);
            System.out.println("安装Agent结果: " + installAgentResult);
            
            // 同步主机状态
            ResponseDTO<Boolean> syncResult = cloudbilityService.syncHostStatus(hostId);
            System.out.println("同步主机状态结果: " + syncResult);
            
            // 更新主机信息
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("description", "更新后的主机描述");
            ResponseDTO<HostDTO> updateResult = cloudbilityService.updateHost(hostId, updateData);
            System.out.println("更新主机信息结果: " + updateResult);
        }
    }

    /**
     * 测试批量操作
     */
    @Test
    public void testBatchOperations() {
        List<Long> hostIds = Arrays.asList(1L, 2L, 3L);
        
        // 批量启动主机
        ResponseDTO<Boolean> batchStartResult = cloudbilityService.batchOperateHosts(hostIds, "start");
        System.out.println("批量启动主机结果: " + batchStartResult);
        
        // 批量同步主机状态
        ResponseDTO<Boolean> batchSyncResult = cloudbilityService.batchSyncHostStatus(hostIds);
        System.out.println("批量同步主机状态结果: " + batchSyncResult);
        
        // 批量删除主机
        ResponseDTO<Boolean> batchDeleteResult = cloudbilityService.deleteHosts(hostIds);
        System.out.println("批量删除主机结果: " + batchDeleteResult);
    }
}
