<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="64938813-c779-4b9e-b119-c1d30d9f5b21" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
    <option name="explicitlyDisabledProfiles" value="prod" />
  </component>
  <component name="OptimizeOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2yikiaRZOI211xoR5CnkvrqAxZ9" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.tsa-parent [clean].executor": "Run",
    "Maven.tsa-parent [package].executor": "Run",
    "Maven.tsa-unified [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.AdminApplication.executor": "Debug",
    "Spring Boot.StudentApplication.executor": "Debug",
    "Spring Boot.UnifiedApplication.executor": "Debug",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/Volumes/Data/Code/Project/soms/soms-api/soms-admin/src/main/java/cn/edu/xmut/soms/admin/module/asset/server/constant",
    "llm.McpServerEditPanel.switcher": "1",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/soms-admin/src/main/java/cn/edu/xmut/soms/admin/module/asset/server/constant" />
      <recent name="$PROJECT_DIR$/soms-admin/src/main/java/cn/edu/xmut/soms/admin/module/asset/server/domain/entity" />
      <recent name="$PROJECT_DIR$/soms-base/src/main/java/cn/edu/xmut/soms/base/module/support/file" />
      <recent name="$PROJECT_DIR$/soms-base/src/main/java/cn/edu/xmut/soms/base/module/support/file/service" />
      <recent name="$PROJECT_DIR$/tsa-unified/src/main/java/cn/edu/xmut/tsa/unified/module/student" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="cn.edu.xmut.soms.admin.module.asset.cloudbility" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="cn.edu.xmut.tsa.base.common.enumeration" />
      <recent name="cn.edu.xmut.tsa.unified.module.student.info.domain.form" />
      <recent name="cn.edu.xmut.tsa.admin.constant" />
      <recent name="cn.edu.xmut.tsa.student.constant" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="soms-base" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="soms-base" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="soms-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.edu.xmut.soms.admin.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="64938813-c779-4b9e-b119-c1d30d9f5b21" name="更改" comment="" />
      <created>1750324908979</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750324908979</updated>
      <workItem from="1750324910462" duration="2783000" />
      <workItem from="1750387534028" duration="6581000" />
      <workItem from="1750397674961" duration="97000" />
      <workItem from="1750397778241" duration="47509000" />
      <workItem from="1751091993741" duration="1377000" />
      <workItem from="1751245754599" duration="2653000" />
      <workItem from="1751331721303" duration="20517000" />
      <workItem from="1751419551657" duration="1560000" />
      <workItem from="1751437926690" duration="17425000" />
      <workItem from="1751942897437" duration="551000" />
      <workItem from="1751943478976" duration="1342000" />
      <workItem from="1751944837012" duration="319000" />
      <workItem from="1751945170580" duration="740000" />
      <workItem from="1751945921602" duration="3066000" />
      <workItem from="1751953265680" duration="36000" />
      <workItem from="1751953324955" duration="29000" />
      <workItem from="1751953387535" duration="2224000" />
      <workItem from="1751960626370" duration="7663000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/tsa-student/src/main/java/cn/edu/xmut/tsa/student/interceptor/AdminInterceptor.java</url>
          <line>62</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>