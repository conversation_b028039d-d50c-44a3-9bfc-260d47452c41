<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerDao">

    <select id="queryPage" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
            s.server_id,
            s.server_type,
            s.server_name,
            s.hostname,
            s.operating_system,
            s.public_ip,
            s.private_ip,
            s.host_status,
            s.cpu_cores,
            s.memory_size,
            s.host_description,
            s.system_disk_size,
            s.data_disk_size,
            s.is_linked_system,
            s.internet_access,
            s.usage_status,
            s.purpose,
            s.ip_list,
            s.manual_ip,
            s.nat_ip,
            s.location,
            s.department_id,
            d.department_name,
            s.responsible_person,
            s.contact_info,
            s.purchase_date,
            s.warranty_date,
            s.vendor,
            s.model,
            s.serial_number,
            s.asset_number,
            s.sync_source,
            s.external_id,
            s.last_sync_time,
            s.sync_status,
            s.sync_error_msg,
            s.create_time,
            s.update_time
        FROM t_server s
        LEFT JOIN t_department d ON s.department_id = d.department_id
        <where>
            s.is_deleted = #{queryForm.isDeleted}
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                    s.server_name LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR s.hostname LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR s.public_ip LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR s.private_ip LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR s.manual_ip LIKE CONCAT('%', #{queryForm.keywords}, '%')
                )
            </if>
            <if test="queryForm.serverType != null">
                AND s.server_type = #{queryForm.serverType}
            </if>
            <if test="queryForm.hostStatus != null and queryForm.hostStatus != ''">
                AND s.host_status = #{queryForm.hostStatus}
            </if>
            <if test="queryForm.usageStatus != null and queryForm.usageStatus != ''">
                AND s.usage_status = #{queryForm.usageStatus}
            </if>
            <if test="queryForm.departmentId != null">
                AND s.department_id = #{queryForm.departmentId}
            </if>
            <if test="queryForm.syncSource != null and queryForm.syncSource != ''">
                AND s.sync_source = #{queryForm.syncSource}
            </if>
            <if test="queryForm.isLinkedSystem != null">
                AND s.is_linked_system = #{queryForm.isLinkedSystem}
            </if>
            <if test="queryForm.internetAccess != null">
                AND s.internet_access = #{queryForm.internetAccess}
            </if>
            <if test="queryForm.responsiblePerson != null and queryForm.responsiblePerson != ''">
                AND s.responsible_person LIKE CONCAT('%', #{queryForm.responsiblePerson}, '%')
            </if>
            <if test="queryForm.vendor != null and queryForm.vendor != ''">
                AND s.vendor LIKE CONCAT('%', #{queryForm.vendor}, '%')
            </if>
            <if test="queryForm.createTimeBegin != null">
                AND DATE(s.create_time) >= #{queryForm.createTimeBegin}
            </if>
            <if test="queryForm.createTimeEnd != null">
                AND DATE(s.create_time) <= #{queryForm.createTimeEnd}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>

    <select id="selectByExternalId" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity">
        SELECT * FROM t_server
        WHERE external_id = #{externalId}
        AND sync_source = #{syncSource}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <select id="selectByIpAddress" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity">
        SELECT * FROM t_server
        WHERE (
            public_ip = #{ipAddress}
            OR private_ip = #{ipAddress}
            OR manual_ip = #{ipAddress}
            OR nat_ip = #{ipAddress}
            OR JSON_CONTAINS(ip_list, JSON_QUOTE(#{ipAddress}))
        )
        AND is_deleted = 0
    </select>

    <select id="selectByHostname" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity">
        SELECT * FROM t_server
        WHERE hostname = #{hostname}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <update id="batchUpdateSyncStatus">
        UPDATE t_server
        SET sync_status = #{syncStatus},
            sync_error_msg = #{syncErrorMsg},
            last_sync_time = NOW()
        WHERE server_id IN
        <foreach collection="serverIds" item="serverId" open="(" separator="," close=")">
            #{serverId}
        </foreach>
    </update>

    <select id="countByServerType" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
            server_type,
            COUNT(*) as total_count
        FROM t_server
        WHERE is_deleted = 0
        GROUP BY server_type
    </select>

    <select id="countByHostStatus" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
            host_status,
            COUNT(*) as total_count
        FROM t_server
        WHERE is_deleted = 0
        GROUP BY host_status
    </select>

    <select id="selectExpiringWarranty" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
            server_id,
            server_name,
            hostname,
            vendor,
            model,
            warranty_date,
            responsible_person,
            contact_info
        FROM t_server
        WHERE is_deleted = 0
        AND warranty_date IS NOT NULL
        AND warranty_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL #{days} DAY)
        ORDER BY warranty_date ASC
    </select>

</mapper>
