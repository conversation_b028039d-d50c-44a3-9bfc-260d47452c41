<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerSyncLogDao">

    <select id="selectRecentLogs" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerSyncLogEntity">
        SELECT
            log_id,
            sync_type,
            sync_source,
            sync_status,
            start_time,
            end_time,
            total_count,
            success_count,
            fail_count,
            error_message,
            sync_detail,
            operator_id,
            operator_name,
            create_time
        FROM t_server_sync_log
        <where>
            <if test="syncSource != null and syncSource != ''">
                sync_source = #{syncSource}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectLogsByTimeRange" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerSyncLogEntity">
        SELECT
            log_id,
            sync_type,
            sync_source,
            sync_status,
            start_time,
            end_time,
            total_count,
            success_count,
            fail_count,
            error_message,
            sync_detail,
            operator_id,
            operator_name,
            create_time
        FROM t_server_sync_log
        <where>
            <if test="syncSource != null and syncSource != ''">
                sync_source = #{syncSource}
            </if>
            <if test="startTime != null">
                AND start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND start_time <= #{endTime}
            </if>
        </where>
        ORDER BY start_time DESC
    </select>

    <delete id="deleteExpiredLogs">
        DELETE FROM t_server_sync_log
        WHERE create_time < #{beforeTime}
    </delete>

</mapper>
