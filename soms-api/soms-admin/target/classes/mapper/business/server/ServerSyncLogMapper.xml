<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerSyncLogDao">

    <select id="queryPage" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerSyncLogVO">
        SELECT
        sync_log_id,
        sync_source,
        sync_type,
        sync_status,
        total_count,
        success_count,
        failed_count,
        sync_message,
        error_message,
        start_time,
        end_time,
        duration,
        creator_name,
        create_time
        FROM t_server_sync_log
        WHERE 1 = 1
        <if test="syncSource != null and syncSource != ''">
            AND sync_source = #{syncSource}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
