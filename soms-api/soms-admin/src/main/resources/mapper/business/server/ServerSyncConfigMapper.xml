<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerSyncConfigDao">

    <select id="getBySyncSource"
            resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerSyncConfigEntity">
        SELECT *
        FROM t_server_sync_config
        WHERE sync_source = #{syncSource}
        LIMIT 1
    </select>

</mapper>
