<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerDao">

    <select id="queryPage" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
        s.server_id,
        s.server_name,
        s.server_type,
        s.operating_system,
        s.public_ip,
        s.private_ip,
        s.host_status,
        s.cpu_cores,
        s.memory_size,
        s.host_description,
        s.system_disk_size,
        s.data_disk_size,
        s.usage_status,
        s.purpose,
        s.ip_list,
        s.manual_ip,
        s.nat_ip,
        s.is_linked_system,
        s.internet_access,
        s.data_source,
        s.external_id,
        s.last_sync_time,
        s.create_time,
        s.update_time
        FROM t_server s
        WHERE s.is_deleted = 0
        <if test="queryForm.serverName != null and queryForm.serverName != ''">
            AND s.server_name LIKE CONCAT('%', #{queryForm.serverName}, '%')
        </if>
        <if test="queryForm.serverType != null">
            AND s.server_type = #{queryForm.serverType}
        </if>
        <if test="queryForm.publicIp != null and queryForm.publicIp != ''">
            AND s.public_ip LIKE CONCAT('%', #{queryForm.publicIp}, '%')
        </if>
        <if test="queryForm.privateIp != null and queryForm.privateIp != ''">
            AND s.private_ip LIKE CONCAT('%', #{queryForm.privateIp}, '%')
        </if>
        <if test="queryForm.hostStatus != null and queryForm.hostStatus != ''">
            AND s.host_status = #{queryForm.hostStatus}
        </if>
        <if test="queryForm.dataSource != null and queryForm.dataSource != ''">
            AND s.data_source = #{queryForm.dataSource}
        </if>
        <if test="queryForm.isLinkedSystem != null">
            AND s.is_linked_system = #{queryForm.isLinkedSystem}
        </if>
        <if test="queryForm.internetAccess != null">
            AND s.internet_access = #{queryForm.internetAccess}
        </if>
        ORDER BY s.update_time DESC
    </select>

    <select id="getByExternalId" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity">
        SELECT *
        FROM t_server
        WHERE external_id = #{externalId}
          AND data_source = #{dataSource}
          AND is_deleted = 0
        LIMIT 1
    </select>

    <insert id="batchInsertOrUpdate">
        INSERT INTO t_server (
        server_name, server_type, operating_system, public_ip, private_ip,
        host_status, cpu_cores, memory_size, host_description, system_disk_size,
        data_disk_size, usage_status, purpose, ip_list, manual_ip, nat_ip,
        is_linked_system, internet_access, data_source, external_id, last_sync_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.serverName}, #{item.serverType}, #{item.operatingSystem},
            #{item.publicIp}, #{item.privateIp}, #{item.hostStatus}, #{item.cpuCores},
            #{item.memorySize}, #{item.hostDescription}, #{item.systemDiskSize},
            #{item.dataDiskSize}, #{item.usageStatus}, #{item.purpose}, #{item.ipList},
            #{item.manualIp}, #{item.natIp}, #{item.isLinkedSystem}, #{item.internetAccess},
            #{item.dataSource}, #{item.externalId}, #{item.lastSyncTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        server_name = VALUES(server_name),
        server_type = VALUES(server_type),
        operating_system = VALUES(operating_system),
        public_ip = VALUES(public_ip),
        private_ip = VALUES(private_ip),
        host_status = VALUES(host_status),
        cpu_cores = VALUES(cpu_cores),
        memory_size = VALUES(memory_size),
        host_description = VALUES(host_description),
        system_disk_size = VALUES(system_disk_size),
        data_disk_size = VALUES(data_disk_size),
        usage_status = VALUES(usage_status),
        purpose = VALUES(purpose),
        ip_list = VALUES(ip_list),
        manual_ip = VALUES(manual_ip),
        nat_ip = VALUES(nat_ip),
        is_linked_system = VALUES(is_linked_system),
        internet_access = VALUES(internet_access),
        last_sync_time = VALUES(last_sync_time),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <update id="batchUpdateDeleted">
        UPDATE t_server SET
        is_deleted = #{isDeleted},
        delete_time = CASE WHEN #{isDeleted} = 1 THEN NOW() ELSE NULL END
        WHERE server_id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

</mapper>
