---
type: "always_apply"
---

# Java 开发规范

## 目录结构规范

```bash
src                               源码目录
|-- common                            各个项目的通用类库
|-- config                            项目的配置信息
|-- constant                          全局公共常量
|-- handler                           全局处理器
|-- interceptor                       全局连接器
|-- listener                          全局监听器
|-- module                            各个业务
|-- |--- employee                         员工模块
|-- |--- role                             角色模块
|-- |--- login                            登录模块
|-- third                             三方服务，比如redis, oss，微信sdk等等
|-- util                              全局工具类
|-- Application.java                  启动类
```

### common 目录

common 目录用于存放各个项目通用的项目

```bash
src 源码目录
|-- common 各个项目的通用类库
|-- |--- anno          通用注解，比如权限，登录等等
|-- |--- constant      通用常量，比如 ResponseCodeConst
|-- |--- domain        全局的 javabean，比如 BaseEntity,PageParamDTO 等
|-- |--- exception     全局异常，如 BusinessException
|-- |--- json          json 类库，如 LongJsonDeserializer，LongJsonSerializer
|-- |--- swagger       swagger 文档
|-- |--- validator     适合各个项目的通用 validator，如 CheckEnum，CheckBigDecimal 等
```

### module 目录

module 目录里写项目的各个业务，每个业务一个独立的顶级文件夹，在文件里进行 mvc 的相关划分。 其中，domain 包里存放 entity, dto, vo，bo 等 javabean 对象

```bash
src
|-- module                         所有业务模块
|-- |-- role                          角色模块
|-- |-- |--RoleController.java              controller
|-- |-- |--RoleConst.java                   role相关的常量
|-- |-- |--RoleService.java                 service
|-- |-- |--RoleDao.java                     dao
|-- |-- |--domain                           domain
|-- |-- |-- |-- RoleEntity.java                  表对应实体
|-- |-- |-- |-- RoleForm.java                     请求Form对象
|-- |-- |-- |-- RoleVO.java                      返回对象
|-- |-- employee                      员工模块
|-- |-- login                         登录模块
|-- |-- email                         邮件模块
|-- |-- ....                          其他
```

## 命名规范

### 包名规范

全部采用小写方式， 以中划线分隔。

```bash
# 正例
mall-management-system
order-service-client
user-api

# 反例
mall_management-system
mallManagementSystem
orderServiceClient
```

### javabean 命名规范

#### javabean 的整体要求

- 不得有任何的业务逻辑或者计算
- 基本数据类型必须使用包装类型（`Integer`, `Double`、`Boolean` 等）
- 不允许有任何的默认值
- 每个属性必须添加注释，并且必须使用多行注释。
- 必须使用 `lombok` 简化 `getter/setter` 方法
- 建议对象使用 `lombok` 的 `@Builder` ，`@NoArgsConstructor`，同时使用这两个注解，简化对象构造方法以及 `set` 方法。

#### javabean 名字划分

- `XxxEntity` 数据库持久对象
- `XxxVO` 返回前端对象
- `XxxForm` 前端请求对象
- `XxxDTO` 数据传输对象
- `XxxBO` 内部处理对象

#### 数据对象 XxxxEntity 要求

- 以 `Entity` 为结尾
- Xxxx 与数据库表名保持一致
- 类中字段要与数据库字段保持一致，不能缺失或者多余
- 类中的每个字段添加注释，并与数据库注释保持一致
- 不允许有组合
- 项目内的日期类型必须统一，使用 `java.time.LocalDateTime` 或者 `java.time.LocalDate`

#### 请求对象 XxxxForm 要求

- 不可以继承自 `Entity`
- `Form` 可以继承、组合其他 `DTO`，`VO`，`BO` 等对象
- `Form` 只能用于前端、RPC 的请求参数

#### 返回对象 XxxxVO 要求

- 不可继承自 `Entity`
- `VO` 可以继承、组合其他 `DTO`，`VO`，`BO` 等对象
- `VO` 只能用于返回前端、rpc 的业务数据封装对象

#### 业务对象 XxxxBO 要求

- 不可以继承自 `Entity`
- `BO` 对象只能用于 `service`，`manager`，`dao` 层，不得用于 `controller` 层

### boolean 类型的属性命名规范

`boolean` 类型的类属性和数据表字段都统一使用 `flag` 结尾

## 方法参数规范

无论是 `controller`，`service`，`manager`，`dao` 亦或是其他 class 的代码，每个方法最多 5 个参数，如果超出 5 个参数的话，要封装成 `javabean` 对象。

## MVC 规范

### 整体分层

- `controller` 层
- `service` 层
- `manager` 层
- `dao` 层

### controller 层

`controller` 层主要负责接收请求，参数校验，调用 `service` 层的业务逻辑，返回结果。

- 只允许在 `method` 上添加 `RequestMapping` 注解，不允许加在 `class` 上
- 只能使用 `get/post` 方法。url 命名遵循：`/业务模块/子模块/动作`；其中业务模块和子模块使用名词， 动作使用动词
- `controller` 每个方法要保持简洁
  - 不做任何的业务逻辑操作
  - 不做任何的参数、业务校验，参数校验只允许使用@Valid 注解做简单的校验
  - 不做任何的数据组合、拼装、赋值等操作
- 只能在 `controller` 层获取当前请求用户，并传递给 `service` 层。

### service 层

`service` 层主要负责业务逻辑处理，数据校验，数据转换等。

- 合理拆分 `service` 业务
  不建议 `service` 文件行数太大，如果业务较大，请拆分为多个 `service`；
- 谨慎使用 `@Transactional` 事务注解

  1. 谨慎使用 `@Transactional 事务注解的使用，不要简单对 `service`的方法添加个`@Transactional` 注解就觉得万事大吉了。
  2. 应当合并对数据库的操作，尽量减少添加了 `@Transactional` 方法内的业务逻辑。
  3. `@Transactional` 注解内的 `rollbackFor` 值必须使用异常 `Exception.class`
  4. 将数据在 `service` 层准备好，然后传递给 `manager` 层，由 `manager` 层添加 `@Transactional` 进行数据库操作。

  ```java
    // 反例
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> upOrDown(Long departmentId, Long swapId) {
        // 验证 1
        DepartmentEntity departmentEntity = departmentDao.selectById(departmentId);
        if (departmentEntity == null) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.NOT_EXISTS);
        }
        // 验证 2
        DepartmentEntity swapEntity = departmentDao.selectById(swapId);
        if (swapEntity == null) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.NOT_EXISTS);
        }
        // 验证 3
        Long count = employeeDao.countByDepartmentId(departmentId)
        if (count != null && count > 0) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.EXIST_EMPLOYEE);
        }
        // 操作数据库 4
        Long departmentSort = departmentEntity.getSort();
        departmentEntity.setSort(swapEntity.getSort());
        departmentDao.updateById(departmentEntity);
        swapEntity.setSort(departmentSort);
        departmentDao.updateById(swapEntity);
        return ResponseDTO.succ();
    }

    // 正例
    DepartmentService.java

    public ResponseDTO<String> upOrDown(Long departmentId, Long swapId) {
        DepartmentEntity departmentEntity = departmentDao.selectById(departmentId);
        if (departmentEntity == null) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.NOT_EXISTS);
        }
        DepartmentEntity swapEntity = departmentDao.selectById(swapId);
        if (swapEntity == null) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.NOT_EXISTS);
        }
        Long count = employeeDao.countByDepartmentId(departmentId)
        if (count != null && count > 0) {
            return ResponseDTO.wrap(DepartmentResponseCodeConst.EXIST_EMPLOYEE);
        }
        departmentManager.upOrDown(departmentSort,swapEntity);
        return ResponseDTO.succ();
    }


    DepartmentManager.java

    @Transactional(rollbackFor = Throwable.class)
    public void upOrDown(DepartmentEntity departmentEntity ,DepartmentEntity swapEntity){
        Long departmentSort = departmentEntity.getSort();
        departmentEntity.setSort(swapEntity.getSort());
        departmentDao.updateById(departmentEntity);
        swapEntity.setSort(departmentSort);
        departmentDao.updateById(swapEntity);
    }
  ```

### manager 层

- 对第三方平台封装的层，预处理返回结果及转化异常信息
- 对 `Service` 层通用能力的下沉，如缓存方案、中间件通用处理
- 与 `DAO` 层交互，对多个 `DAO` 的组合复用

### dao 层

- 优先使用 `mybatis-plus` 框架
- 所有 `Dao` 继承自 `BaseMapper`
- 禁止使用 `Mybatis-plus` 的 `Wrapper` 条件构建器
- 禁止直接在 `mybatis.xml` 中写死常量，应从 `dao` 中传入到 `xml` 中
- 建议在 xml 中的 `join` 关联写法使用表名的全称，而不是用别名

## 其他规范

### 函数顺序

函数顺序按照以下顺序排列：

1. 查询类方法
2. 新增类方法
3. 修改类方法
4. 删除类方法
5. 其他类方法
